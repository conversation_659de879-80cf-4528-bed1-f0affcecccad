# Bili Taxi - Application de Transport Complète

Une application de taxi moderne et complète développée en Flutter avec une architecture MVVM propre, des données mock et une interface utilisateur moderne en français.

## 🚀 Fonctionnalités

### 👤 Utilisateur
- **Sélection de destination** : Recherche de lieux avec barre de recherche et sélection sur carte
- **Types de véhicules** : Économique, Confort, Premium, Luxe, SUV
- **Estimation de prix et temps** : Calcul automatique avant la réservation
- **Suivi en temps réel** : Visualisation de la position du conducteur
- **Historique des courses** : Consultation des trajets précédents
- **Évaluations** : Système de notation des conducteurs
- **Lieux favoris** : Sauvegarde des adresses fréquentes
- **Méthodes de paiement** : Carte bancaire, Stripe, espèces

### 🚗 Conducteur
- **Statut en ligne/hors ligne** : Contrôle de la disponibilité
- **Réception de courses** : Acceptation/refus des demandes
- **Navigation intégrée** : Guidage vers les points de prise en charge
- **Suivi des gains** : Tableau de bord des revenus quotidiens et totaux
- **Historique des courses** : Consultation des trajets effectués
- **Gestion du véhicule** : Informations et documents du véhicule
- **Évaluations** : Système de notation des passagers

### 👨‍💼 Administrateur
- **Tableau de bord** : Vue d'ensemble des statistiques
- **Gestion des utilisateurs** : Administration des comptes utilisateurs et conducteurs
- **Rapports détaillés** : Génération de rapports financiers, utilisateurs, conducteurs
- **Génération de factures PDF** : Création automatique de factures
- **Analyses** : Statistiques avancées et métriques de performance
- **Activité en temps réel** : Monitoring des activités de la plateforme

## 🏗️ Architecture

### Structure MVVM
```
lib/
├── models/          # Modèles de données
├── views/           # Interfaces utilisateur
│   ├── auth/        # Authentification
│   ├── user/        # Interface utilisateur
│   ├── driver/      # Interface conducteur
│   ├── admin/       # Interface administrateur
│   └── shared/      # Composants partagés
├── viewmodels/      # Logique métier
├── services/        # Services et API
├── constants/       # Constantes et couleurs
├── utils/           # Utilitaires
└── widgets/         # Widgets réutilisables
```

### Modèles de Données
- **User** : Utilisateur de base avec informations personnelles
- **Driver** : Conducteur avec véhicule et statut
- **Admin** : Administrateur avec permissions
- **Ride** : Course avec statuts et paiement
- **Vehicle** : Véhicule avec caractéristiques
- **Payment** : Paiement avec méthodes multiples
- **Location** : Localisation avec géocodage

## 🎨 Design

### Couleurs
- **Primaire** : Vert (#2E7D32) - Représente la confiance et la nature
- **Secondaire** : Orange (#FF9800) - Énergie et dynamisme
- **Accent** : Turquoise (#03DAC6) - Modernité et technologie

### Interface Utilisateur
- Design moderne et épuré
- Navigation intuitive
- Animations fluides
- Responsive design
- Thème cohérent

## 🔧 Installation et Configuration

### Prérequis
- Flutter SDK (3.8.1+)
- Dart SDK
- Android Studio / VS Code
- Émulateur Android ou appareil physique

### Installation
```bash
# Cloner le repository
git clone <repository-url>
cd bili-taxi

# Installer les dépendances
flutter pub get

# Lancer l'application
flutter run
```

### Configuration Google Maps
1. Obtenir une clé API Google Maps
2. Ajouter la clé dans `android/app/src/main/AndroidManifest.xml`
3. Activer les APIs nécessaires dans Google Cloud Console

## 👥 Comptes de Test

### Utilisateur
- **Email** : <EMAIL>
- **Mot de passe** : password123

### Conducteur
- **Email** : <EMAIL>
- **Mot de passe** : password123

### Administrateur
- **Email** : <EMAIL>
- **Mot de passe** : admin123

## 📱 Fonctionnalités Techniques

### Dépendances Principales
- **google_maps_flutter** : Intégration Google Maps
- **geolocator** : Géolocalisation
- **provider** : Gestion d'état
- **http/dio** : Requêtes réseau
- **shared_preferences** : Stockage local
- **pdf** : Génération de PDF
- **stripe_payment** : Paiements Stripe

### Fonctionnalités Avancées
- Suivi GPS en temps réel
- Calcul d'itinéraires optimisés
- Notifications push
- Paiements sécurisés
- Génération de rapports PDF
- Système de notation bidirectionnel

## 🚀 Prochaines Étapes

### Intégrations à Venir
- [ ] Intégration Google Maps réelle avec clé API
- [ ] Backend Firebase/Supabase
- [ ] Notifications push
- [ ] Paiements Stripe réels
- [ ] Géolocalisation en temps réel
- [ ] Chat en temps réel
- [ ] Courses partagées
- [ ] Courses programmées

### Améliorations
- [ ] Tests unitaires et d'intégration
- [ ] CI/CD Pipeline
- [ ] Monitoring et analytics
- [ ] Optimisation des performances
- [ ] Accessibilité
- [ ] Internationalisation

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! Veuillez consulter le guide de contribution pour plus d'informations.

## 📞 Support

Pour toute question ou support, contactez-nous à : <EMAIL>

---

**Bili Taxi** - Votre transport en toute simplicité 🚕
