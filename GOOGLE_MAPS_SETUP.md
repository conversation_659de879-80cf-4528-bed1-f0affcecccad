# Configuration Google Maps pour Bili Taxi

## ✅ ÉTAT ACTUEL
- **Google Maps intégré** ✅ - Cartes réelles avec marqueurs et polylines
- **Google Places API intégré** ✅ - Recherche de lieux fonctionnelle
- **Sélection de localisation** ✅ - Pickup/destination sur carte et via recherche
- **Routing et polylines** ✅ - Calcul d'itinéraires et affichage des trajets
- **ViewModels séparés** ✅ - Logique métier séparée des vues
- **UI et logique connectées** ✅ - Toutes les vues utilisent les ViewModels

## 🗺️ Étapes pour activer votre clé API Google Maps

### 1. Obtenir une clé API Google Maps

1. Aller sur [Google Cloud Console](https://console.cloud.google.com/)
2. Créer un nouveau projet ou sélectionner un projet existant
3. Activer les APIs suivantes :
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
   - Directions API
   - Geocoding API

4. Créer une clé API :
   - Aller dans "Credentials"
   - Cliquer sur "Create Credentials" > "API Key"
   - Copier la clé générée

### 2. Configuration Android

Ajouter la clé API dans `android/app/src/main/AndroidManifest.xml` :

```xml
<application>
    <!-- Autres configurations -->
    
    <meta-data
        android:name="com.google.android.geo.API_KEY"
        android:value="VOTRE_CLE_API_ICI" />
</application>
```

### 3. Configuration iOS

Ajouter la clé API dans `ios/Runner/AppDelegate.swift` :

```swift
import UIKit
import Flutter
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("VOTRE_CLE_API_ICI")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

### 4. Activer votre clé API dans l'application

Remplacez les clés API mock dans les fichiers suivants :

#### `lib/services/maps_service.dart`
```dart
static const String _apiKey = 'VOTRE_CLE_API_ICI'; // Ligne 8
```

#### `lib/services/places_service.dart`
```dart
static const String _apiKey = 'VOTRE_CLE_API_ICI'; // Ligne 25
```

#### `lib/services/directions_service.dart`
```dart
static const String _apiKey = 'VOTRE_CLE_API_ICI'; // Ligne 45
```

## ✅ FONCTIONNALITÉS DÉJÀ IMPLÉMENTÉES

### Google Maps intégrée
- Cartes réelles avec position initiale
- Marqueurs pour taxis, pickup, destination
- Polylines pour les itinéraires
- Contrôles de zoom et navigation
- Sélection de destination par tap sur la carte

### Google Places API
- Recherche de lieux en temps réel
- Autocomplétion des adresses
- Détails des lieux sélectionnés
- Lieux favoris prédéfinis

### Routing et Navigation
- Calcul d'itinéraires entre deux points
- Affichage des polylines sur la carte
- Estimation de temps et distance
- Ajustement automatique de la caméra

### 5. Ajouter la géolocalisation en temps réel

```dart
// Dans les ViewModels
import 'package:geolocator/geolocator.dart';

class LocationService {
  static Future<Position> getCurrentLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Les services de localisation sont désactivés.');
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Les permissions de localisation sont refusées');
      }
    }

    return await Geolocator.getCurrentPosition();
  }

  static Stream<Position> getLocationStream() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    );
  }
}
```

### 6. Ajouter la recherche de lieux

```dart
// Service de recherche de lieux
import 'package:google_places_flutter/google_places_flutter.dart';

class PlacesService {
  static Future<List<PlacePrediction>> searchPlaces(String query) async {
    // Implémentation de la recherche de lieux
    // Utiliser google_places_flutter ou l'API Places directement
  }
}
```

### 7. Calcul d'itinéraires

```dart
// Service de calcul d'itinéraires
class DirectionsService {
  static Future<List<LatLng>> getRoute(LatLng origin, LatLng destination) async {
    // Utiliser l'API Directions de Google
    // Retourner les points de l'itinéraire pour dessiner la polyline
  }
}
```

### 8. Permissions requises

#### Android (`android/app/src/main/AndroidManifest.xml`)
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.INTERNET" />
```

#### iOS (`ios/Runner/Info.plist`)
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>Cette app a besoin d'accéder à votre localisation pour vous proposer des courses à proximité.</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Cette app a besoin d'accéder à votre localisation pour le suivi des courses.</string>
```

### 9. Réactiver Stripe (optionnel)

Pour réactiver les paiements Stripe, remplacer dans `pubspec.yaml` :
```yaml
# stripe_payment: ^1.1.4  # Obsolète
flutter_stripe: ^10.1.1   # Version moderne
```

### 10. Test de l'intégration

1. Ajouter votre clé API
2. Lancer `flutter clean && flutter pub get`
3. Tester sur un appareil physique (la géolocalisation ne fonctionne pas sur simulateur)

## 🚀 Fonctionnalités avancées à ajouter

- Suivi en temps réel des conducteurs
- Calcul automatique des prix basé sur la distance
- Navigation turn-by-turn
- Géofencing pour les zones de service
- Heatmap des demandes de courses

## 📝 Notes importantes

- Les cartes Google Maps nécessitent un appareil physique pour la géolocalisation
- Configurer la facturation sur Google Cloud Console
- Limiter l'utilisation de l'API pour contrôler les coûts
- Tester sur iOS et Android séparément

L'application est déjà structurée pour recevoir ces intégrations facilement !
