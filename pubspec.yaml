name: bili_taxi
description: "Application de taxi Bili - Une solution complète de transport"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.1.0

  # Maps & Location
  google_maps_flutter: ^2.6.1
  google_polyline_algorithm: ^3.1.0
  google_places_flutter: ^2.0.9
  geolocator: ^11.0.0
  geocoding: ^3.0.0
  google_directions_api: ^0.10.0

  # State Management
  provider: ^6.1.2

  # HTTP & API
  http: ^1.2.1
  dio: ^5.4.3+1

  # Storage & Database
  shared_preferences: ^2.2.3
  sqflite: ^2.3.3+1

  # Authentication & Security
  crypto: ^3.0.3

  # Payment (Stripe temporairement désactivé pour iOS)
  # stripe_payment: ^1.1.4

  # PDF Generation
  pdf: ^3.10.8
  printing: ^5.12.0

  # Image & Media
  image_picker: ^1.0.8
  cached_network_image: ^3.3.1

  # Utils
  intl: ^0.19.0
  uuid: ^4.4.0
  url_launcher: ^6.2.6
  permission_handler: ^11.3.1

  # UI Components
  flutter_rating_bar: ^4.0.1
  shimmer: ^3.0.0
  lottie: ^3.1.2
  persistent_bottom_nav_bar_v2: ^6.1.0
  animated_bottom_navigation_bar: ^1.4.0
  modal_bottom_sheet: ^3.0.0
  flutter_animate: ^4.5.2
  glass_kit: ^4.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
