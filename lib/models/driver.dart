import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/vehicle.dart';

enum DriverStatus { offline, online, busy, unavailable }

class Driver extends User {
  final String licenseNumber;
  final DateTime licenseExpiry;
  final String? licenseImage;
  final Vehicle? vehicle;
  final DriverStatus driverStatus;
  final double driverRating;
  final int totalTrips;
  final double totalEarnings;
  final bool isVerified;
  final DateTime? lastActiveAt;
  final List<String> documents;
  final Map<String, dynamic>? driverPreferences;

  Driver({
    required String id,
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phone,
    String? profileImage,
    UserStatus status = UserStatus.active,
    required DateTime createdAt,
    DateTime? lastLoginAt,
    Location? currentLocation,
    double rating = 0.0,
    int totalRides = 0,
    bool isEmailVerified = false,
    bool isPhoneVerified = false,
    String? fcmToken,
    Map<String, dynamic>? preferences,
    required this.licenseNumber,
    required this.licenseExpiry,
    this.licenseImage,
    this.vehicle,
    this.driverStatus = DriverStatus.offline,
    this.driverRating = 0.0,
    this.totalTrips = 0,
    this.totalEarnings = 0.0,
    this.isVerified = false,
    this.lastActiveAt,
    this.documents = const [],
    this.driverPreferences,
  }) : super(
          id: id,
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          profileImage: profileImage,
          userType: UserType.driver,
          status: status,
          createdAt: createdAt,
          lastLoginAt: lastLoginAt,
          currentLocation: currentLocation,
          rating: rating,
          totalRides: totalRides,
          isEmailVerified: isEmailVerified,
          isPhoneVerified: isPhoneVerified,
          fcmToken: fcmToken,
          preferences: preferences,
        );

  bool get isOnline => driverStatus == DriverStatus.online;
  bool get isAvailable => driverStatus == DriverStatus.online;
  bool get isBusy => driverStatus == DriverStatus.busy;

  factory Driver.fromJson(Map<String, dynamic> json) {
    return Driver(
      id: json['id'],
      email: json['email'],
      password: json['password'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      phone: json['phone'],
      profileImage: json['profileImage'],
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${json['status']}',
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
      currentLocation: json['currentLocation'] != null 
          ? Location.fromJson(json['currentLocation']) 
          : null,
      rating: json['rating']?.toDouble() ?? 0.0,
      totalRides: json['totalRides'] ?? 0,
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPhoneVerified: json['isPhoneVerified'] ?? false,
      fcmToken: json['fcmToken'],
      preferences: json['preferences'],
      licenseNumber: json['licenseNumber'],
      licenseExpiry: DateTime.parse(json['licenseExpiry']),
      licenseImage: json['licenseImage'],
      vehicle: json['vehicle'] != null 
          ? Vehicle.fromJson(json['vehicle']) 
          : null,
      driverStatus: DriverStatus.values.firstWhere(
        (e) => e.toString() == 'DriverStatus.${json['driverStatus']}',
      ),
      driverRating: json['driverRating']?.toDouble() ?? 0.0,
      totalTrips: json['totalTrips'] ?? 0,
      totalEarnings: json['totalEarnings']?.toDouble() ?? 0.0,
      isVerified: json['isVerified'] ?? false,
      lastActiveAt: json['lastActiveAt'] != null 
          ? DateTime.parse(json['lastActiveAt']) 
          : null,
      documents: List<String>.from(json['documents'] ?? []),
      driverPreferences: json['driverPreferences'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'licenseNumber': licenseNumber,
      'licenseExpiry': licenseExpiry.toIso8601String(),
      'licenseImage': licenseImage,
      'vehicle': vehicle?.toJson(),
      'driverStatus': driverStatus.toString().split('.').last,
      'driverRating': driverRating,
      'totalTrips': totalTrips,
      'totalEarnings': totalEarnings,
      'isVerified': isVerified,
      'lastActiveAt': lastActiveAt?.toIso8601String(),
      'documents': documents,
      'driverPreferences': driverPreferences,
    });
    return json;
  }
}
