enum PaymentMethod { cash, card, stripe, paypal, applePay, googlePay }

enum PaymentStatus { pending, processing, completed, failed, refunded, cancelled }

class Payment {
  final String id;
  final String rideId;
  final PaymentMethod method;
  final PaymentStatus status;
  final double amount;
  final double? tip;
  final double? discount;
  final double totalAmount;
  final String currency;
  final DateTime createdAt;
  final DateTime? processedAt;
  final String? transactionId;
  final String? stripePaymentIntentId;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  Payment({
    required this.id,
    required this.rideId,
    required this.method,
    this.status = PaymentStatus.pending,
    required this.amount,
    this.tip,
    this.discount,
    required this.totalAmount,
    this.currency = 'EUR',
    required this.createdAt,
    this.processedAt,
    this.transactionId,
    this.stripePaymentIntentId,
    this.failureReason,
    this.metadata,
  });

  bool get isPending => status == PaymentStatus.pending;
  bool get isProcessing => status == PaymentStatus.processing;
  bool get isCompleted => status == PaymentStatus.completed;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isRefunded => status == PaymentStatus.refunded;
  bool get isCancelled => status == PaymentStatus.cancelled;

  bool get isCash => method == PaymentMethod.cash;
  bool get isCard => method == PaymentMethod.card;
  bool get isStripe => method == PaymentMethod.stripe;

  String get methodDisplayName {
    switch (method) {
      case PaymentMethod.cash:
        return 'Espèces';
      case PaymentMethod.card:
        return 'Carte bancaire';
      case PaymentMethod.stripe:
        return 'Stripe';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.googlePay:
        return 'Google Pay';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case PaymentStatus.pending:
        return 'En attente';
      case PaymentStatus.processing:
        return 'En cours';
      case PaymentStatus.completed:
        return 'Terminé';
      case PaymentStatus.failed:
        return 'Échec';
      case PaymentStatus.refunded:
        return 'Remboursé';
      case PaymentStatus.cancelled:
        return 'Annulé';
    }
  }

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      rideId: json['rideId'],
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString() == 'PaymentMethod.${json['method']}',
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == 'PaymentStatus.${json['status']}',
      ),
      amount: json['amount'].toDouble(),
      tip: json['tip']?.toDouble(),
      discount: json['discount']?.toDouble(),
      totalAmount: json['totalAmount'].toDouble(),
      currency: json['currency'] ?? 'EUR',
      createdAt: DateTime.parse(json['createdAt']),
      processedAt: json['processedAt'] != null 
          ? DateTime.parse(json['processedAt']) 
          : null,
      transactionId: json['transactionId'],
      stripePaymentIntentId: json['stripePaymentIntentId'],
      failureReason: json['failureReason'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rideId': rideId,
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'tip': tip,
      'discount': discount,
      'totalAmount': totalAmount,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'processedAt': processedAt?.toIso8601String(),
      'transactionId': transactionId,
      'stripePaymentIntentId': stripePaymentIntentId,
      'failureReason': failureReason,
      'metadata': metadata,
    };
  }

  Payment copyWith({
    String? id,
    String? rideId,
    PaymentMethod? method,
    PaymentStatus? status,
    double? amount,
    double? tip,
    double? discount,
    double? totalAmount,
    String? currency,
    DateTime? createdAt,
    DateTime? processedAt,
    String? transactionId,
    String? stripePaymentIntentId,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return Payment(
      id: id ?? this.id,
      rideId: rideId ?? this.rideId,
      method: method ?? this.method,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      tip: tip ?? this.tip,
      discount: discount ?? this.discount,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      transactionId: transactionId ?? this.transactionId,
      stripePaymentIntentId: stripePaymentIntentId ?? this.stripePaymentIntentId,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }
}
