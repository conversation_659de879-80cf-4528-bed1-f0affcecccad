import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/models/location.dart';

enum AdminRole { superAdmin, admin, moderator, support }

enum AdminPermission {
  manageUsers,
  manageDrivers,
  manageRides,
  managePayments,
  viewReports,
  generateInvoices,
  manageSettings,
  viewAnalytics,
  moderateContent,
  handleSupport
}

class Admin extends User {
  final AdminRole role;
  final List<AdminPermission> permissions;
  final String? department;
  final String? employeeId;
  final bool isActive;
  final Map<String, dynamic>? adminSettings;

  Admin({
    required String id,
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phone,
    String? profileImage,
    UserStatus status = UserStatus.active,
    required DateTime createdAt,
    DateTime? lastLoginAt,
    Location? currentLocation,
    double rating = 0.0,
    int totalRides = 0,
    bool isEmailVerified = false,
    bool isPhoneVerified = false,
    String? fcmToken,
    Map<String, dynamic>? preferences,
    required this.role,
    this.permissions = const [],
    this.department,
    this.employeeId,
    this.isActive = true,
    this.adminSettings,
  }) : super(
          id: id,
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          profileImage: profileImage,
          userType: UserType.admin,
          status: status,
          createdAt: createdAt,
          lastLoginAt: lastLoginAt,
          currentLocation: currentLocation,
          rating: rating,
          totalRides: totalRides,
          isEmailVerified: isEmailVerified,
          isPhoneVerified: isPhoneVerified,
          fcmToken: fcmToken,
          preferences: preferences,
        );

  bool get isSuperAdmin => role == AdminRole.superAdmin;
  bool get canManageUsers => permissions.contains(AdminPermission.manageUsers);
  bool get canManageDrivers => permissions.contains(AdminPermission.manageDrivers);
  bool get canViewReports => permissions.contains(AdminPermission.viewReports);
  bool get canGenerateInvoices => permissions.contains(AdminPermission.generateInvoices);
  bool get canViewAnalytics => permissions.contains(AdminPermission.viewAnalytics);

  String get roleDisplayName {
    switch (role) {
      case AdminRole.superAdmin:
        return 'Super Administrateur';
      case AdminRole.admin:
        return 'Administrateur';
      case AdminRole.moderator:
        return 'Modérateur';
      case AdminRole.support:
        return 'Support';
    }
  }

  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      id: json['id'],
      email: json['email'],
      password: json['password'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      phone: json['phone'],
      profileImage: json['profileImage'],
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${json['status']}',
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
      currentLocation: json['currentLocation'] != null 
          ? Location.fromJson(json['currentLocation']) 
          : null,
      rating: json['rating']?.toDouble() ?? 0.0,
      totalRides: json['totalRides'] ?? 0,
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPhoneVerified: json['isPhoneVerified'] ?? false,
      fcmToken: json['fcmToken'],
      preferences: json['preferences'],
      role: AdminRole.values.firstWhere(
        (e) => e.toString() == 'AdminRole.${json['role']}',
      ),
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((p) => AdminPermission.values.firstWhere(
                (e) => e.toString() == 'AdminPermission.$p',
              ))
          .toList() ?? [],
      department: json['department'],
      employeeId: json['employeeId'],
      isActive: json['isActive'] ?? true,
      adminSettings: json['adminSettings'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'role': role.toString().split('.').last,
      'permissions': permissions.map((p) => p.toString().split('.').last).toList(),
      'department': department,
      'employeeId': employeeId,
      'isActive': isActive,
      'adminSettings': adminSettings,
    });
    return json;
  }
}
