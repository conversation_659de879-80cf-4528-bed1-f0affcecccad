import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/models/driver.dart';
import 'package:bili_taxi/models/payment.dart';

enum RideStatus {
  requested,           // Utilisateur demande une course
  driverAccepted,      // Conducteur accepte, attend confirmation utilisateur
  userAccepted,        // Utilisateur accepte le conducteur (= accepted dans l'ancien système)
  userRejected,        // Utilisateur refuse le conducteur
  driverArriving,      // Conducteur en route vers pickup
  driverArrived,       // Conducteur arrivé au pickup
  inProgress,          // Course en cours
  completed,           // Course terminée
  cancelled,           // Course annulée
  failed               // Course échouée (pas de conducteur)
}

enum RideType { standard, scheduled, shared }

class Ride {
  final String id;
  final String userId;
  final String? driverId;
  final Location pickupLocation;
  final Location destinationLocation;
  final RideStatus status;
  final RideType type;
  final DateTime requestedAt;
  final DateTime? acceptedAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final double? estimatedDistance;
  final double? actualDistance;
  final int? estimatedDuration;
  final int? actualDuration;
  final double? estimatedFare;
  final double? actualFare;
  final Payment? payment;
  final String? notes;
  final double? userRating;
  final double? driverRating;
  final String? userReview;
  final String? driverReview;
  final String? cancellationReason;
  final List<Location> routePoints;
  final Map<String, dynamic>? metadata;

  Ride({
    required this.id,
    required this.userId,
    this.driverId,
    required this.pickupLocation,
    required this.destinationLocation,
    this.status = RideStatus.requested,
    this.type = RideType.standard,
    required this.requestedAt,
    this.acceptedAt,
    this.startedAt,
    this.completedAt,
    this.cancelledAt,
    this.estimatedDistance,
    this.actualDistance,
    this.estimatedDuration,
    this.actualDuration,
    this.estimatedFare,
    this.actualFare,
    this.payment,
    this.notes,
    this.userRating,
    this.driverRating,
    this.userReview,
    this.driverReview,
    this.cancellationReason,
    this.routePoints = const [],
    this.metadata,
  });

  bool get isActive => [
        RideStatus.requested,
        RideStatus.driverAccepted,
        RideStatus.userAccepted,
        RideStatus.driverArriving,
        RideStatus.driverArrived,
        RideStatus.inProgress,
      ].contains(status);

  bool get isCompleted => status == RideStatus.completed;
  bool get isCancelled => status == RideStatus.cancelled;
  bool get canBeCancelled => [
        RideStatus.requested,
        RideStatus.driverAccepted,
        RideStatus.userAccepted,
        RideStatus.driverArriving,
        RideStatus.driverArrived,
      ].contains(status);

  Duration? get totalDuration {
    if (requestedAt != null && completedAt != null) {
      return completedAt!.difference(requestedAt);
    }
    return null;
  }

  factory Ride.fromJson(Map<String, dynamic> json) {
    return Ride(
      id: json['id'],
      userId: json['userId'],
      driverId: json['driverId'],
      pickupLocation: Location.fromJson(json['pickupLocation']),
      destinationLocation: Location.fromJson(json['destinationLocation']),
      status: RideStatus.values.firstWhere(
        (e) => e.toString() == 'RideStatus.${json['status']}',
      ),
      type: RideType.values.firstWhere(
        (e) => e.toString() == 'RideType.${json['type']}',
      ),
      requestedAt: DateTime.parse(json['requestedAt']),
      acceptedAt: json['acceptedAt'] != null 
          ? DateTime.parse(json['acceptedAt']) 
          : null,
      startedAt: json['startedAt'] != null 
          ? DateTime.parse(json['startedAt']) 
          : null,
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
      cancelledAt: json['cancelledAt'] != null 
          ? DateTime.parse(json['cancelledAt']) 
          : null,
      estimatedDistance: json['estimatedDistance']?.toDouble(),
      actualDistance: json['actualDistance']?.toDouble(),
      estimatedDuration: json['estimatedDuration'],
      actualDuration: json['actualDuration'],
      estimatedFare: json['estimatedFare']?.toDouble(),
      actualFare: json['actualFare']?.toDouble(),
      payment: json['payment'] != null 
          ? Payment.fromJson(json['payment']) 
          : null,
      notes: json['notes'],
      userRating: json['userRating']?.toDouble(),
      driverRating: json['driverRating']?.toDouble(),
      userReview: json['userReview'],
      driverReview: json['driverReview'],
      cancellationReason: json['cancellationReason'],
      routePoints: (json['routePoints'] as List<dynamic>?)
          ?.map((point) => Location.fromJson(point))
          .toList() ?? [],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'driverId': driverId,
      'pickupLocation': pickupLocation.toJson(),
      'destinationLocation': destinationLocation.toJson(),
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'requestedAt': requestedAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
      'estimatedDistance': estimatedDistance,
      'actualDistance': actualDistance,
      'estimatedDuration': estimatedDuration,
      'actualDuration': actualDuration,
      'estimatedFare': estimatedFare,
      'actualFare': actualFare,
      'payment': payment?.toJson(),
      'notes': notes,
      'userRating': userRating,
      'driverRating': driverRating,
      'userReview': userReview,
      'driverReview': driverReview,
      'cancellationReason': cancellationReason,
      'routePoints': routePoints.map((point) => point.toJson()).toList(),
      'metadata': metadata,
    };
  }

  Ride copyWith({
    String? id,
    String? userId,
    String? driverId,
    Location? pickupLocation,
    Location? destinationLocation,
    RideStatus? status,
    RideType? type,
    DateTime? requestedAt,
    DateTime? acceptedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? cancelledAt,
    double? estimatedDistance,
    double? actualDistance,
    int? estimatedDuration,
    int? actualDuration,
    double? estimatedFare,
    double? actualFare,
    Payment? payment,
    String? notes,
    double? userRating,
    double? driverRating,
    String? userReview,
    String? driverReview,
    String? cancellationReason,
    List<Location>? routePoints,
    Map<String, dynamic>? metadata,
  }) {
    return Ride(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      driverId: driverId ?? this.driverId,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      destinationLocation: destinationLocation ?? this.destinationLocation,
      status: status ?? this.status,
      type: type ?? this.type,
      requestedAt: requestedAt ?? this.requestedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      actualDistance: actualDistance ?? this.actualDistance,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      actualDuration: actualDuration ?? this.actualDuration,
      estimatedFare: estimatedFare ?? this.estimatedFare,
      actualFare: actualFare ?? this.actualFare,
      payment: payment ?? this.payment,
      notes: notes ?? this.notes,
      userRating: userRating ?? this.userRating,
      driverRating: driverRating ?? this.driverRating,
      userReview: userReview ?? this.userReview,
      driverReview: driverReview ?? this.driverReview,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      routePoints: routePoints ?? this.routePoints,
      metadata: metadata ?? this.metadata,
    );
  }
}
