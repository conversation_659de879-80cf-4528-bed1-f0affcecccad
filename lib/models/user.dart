import 'package:bili_taxi/models/location.dart';

enum UserType { user, driver, admin }

enum UserStatus { active, inactive, suspended, pending }

class User {
  final String id;
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String phone;
  final String? profileImage;
  final UserType userType;
  final UserStatus status;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Location? currentLocation;
  final double rating;
  final int totalRides;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final String? fcmToken;
  final Map<String, dynamic>? preferences;

  User({
    required this.id,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    required this.phone,
    this.profileImage,
    required this.userType,
    this.status = UserStatus.active,
    required this.createdAt,
    this.lastLoginAt,
    this.currentLocation,
    this.rating = 0.0,
    this.totalRides = 0,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.fcmToken,
    this.preferences,
  });

  String get fullName => '$firstName $lastName';

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      password: json['password'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      phone: json['phone'],
      profileImage: json['profileImage'],
      userType: UserType.values.firstWhere(
        (e) => e.toString() == 'UserType.${json['userType']}',
      ),
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${json['status']}',
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
      currentLocation: json['currentLocation'] != null 
          ? Location.fromJson(json['currentLocation']) 
          : null,
      rating: json['rating']?.toDouble() ?? 0.0,
      totalRides: json['totalRides'] ?? 0,
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPhoneVerified: json['isPhoneVerified'] ?? false,
      fcmToken: json['fcmToken'],
      preferences: json['preferences'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'password': password,
      'firstName': firstName,
      'lastName': lastName,
      'phone': phone,
      'profileImage': profileImage,
      'userType': userType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'currentLocation': currentLocation?.toJson(),
      'rating': rating,
      'totalRides': totalRides,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'fcmToken': fcmToken,
      'preferences': preferences,
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? password,
    String? firstName,
    String? lastName,
    String? phone,
    String? profileImage,
    UserType? userType,
    UserStatus? status,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Location? currentLocation,
    double? rating,
    int? totalRides,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    String? fcmToken,
    Map<String, dynamic>? preferences,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      password: password ?? this.password,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      userType: userType ?? this.userType,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      currentLocation: currentLocation ?? this.currentLocation,
      rating: rating ?? this.rating,
      totalRides: totalRides ?? this.totalRides,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      fcmToken: fcmToken ?? this.fcmToken,
      preferences: preferences ?? this.preferences,
    );
  }
}
