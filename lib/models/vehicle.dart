enum VehicleType { economy, comfort, premium, luxury, suv }

enum VehicleStatus { active, inactive, maintenance, inspection }

class Vehicle {
  final String id;
  final String make;
  final String model;
  final int year;
  final String color;
  final String licensePlate;
  final VehicleType type;
  final VehicleStatus status;
  final int capacity;
  final String? image;
  final List<String> features;
  final double pricePerKm;
  final double baseFare;
  final String? insuranceNumber;
  final DateTime? insuranceExpiry;
  final String? registrationNumber;
  final DateTime? registrationExpiry;
  final DateTime createdAt;
  final DateTime? lastInspectionAt;

  Vehicle({
    required this.id,
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    required this.type,
    this.status = VehicleStatus.active,
    required this.capacity,
    this.image,
    this.features = const [],
    required this.pricePerKm,
    required this.baseFare,
    this.insuranceNumber,
    this.insuranceExpiry,
    this.registrationNumber,
    this.registrationExpiry,
    required this.createdAt,
    this.lastInspectionAt,
  });

  String get displayName => '$year $make $model';
  
  bool get isActive => status == VehicleStatus.active;
  
  bool get needsInspection {
    if (lastInspectionAt == null) return true;
    return DateTime.now().difference(lastInspectionAt!).inDays > 365;
  }

  bool get insuranceValid {
    if (insuranceExpiry == null) return false;
    return insuranceExpiry!.isAfter(DateTime.now());
  }

  bool get registrationValid {
    if (registrationExpiry == null) return false;
    return registrationExpiry!.isAfter(DateTime.now());
  }

  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'],
      make: json['make'],
      model: json['model'],
      year: json['year'],
      color: json['color'],
      licensePlate: json['licensePlate'],
      type: VehicleType.values.firstWhere(
        (e) => e.toString() == 'VehicleType.${json['type']}',
      ),
      status: VehicleStatus.values.firstWhere(
        (e) => e.toString() == 'VehicleStatus.${json['status']}',
      ),
      capacity: json['capacity'],
      image: json['image'],
      features: List<String>.from(json['features'] ?? []),
      pricePerKm: json['pricePerKm'].toDouble(),
      baseFare: json['baseFare'].toDouble(),
      insuranceNumber: json['insuranceNumber'],
      insuranceExpiry: json['insuranceExpiry'] != null 
          ? DateTime.parse(json['insuranceExpiry']) 
          : null,
      registrationNumber: json['registrationNumber'],
      registrationExpiry: json['registrationExpiry'] != null 
          ? DateTime.parse(json['registrationExpiry']) 
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      lastInspectionAt: json['lastInspectionAt'] != null 
          ? DateTime.parse(json['lastInspectionAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'make': make,
      'model': model,
      'year': year,
      'color': color,
      'licensePlate': licensePlate,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'capacity': capacity,
      'image': image,
      'features': features,
      'pricePerKm': pricePerKm,
      'baseFare': baseFare,
      'insuranceNumber': insuranceNumber,
      'insuranceExpiry': insuranceExpiry?.toIso8601String(),
      'registrationNumber': registrationNumber,
      'registrationExpiry': registrationExpiry?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'lastInspectionAt': lastInspectionAt?.toIso8601String(),
    };
  }

  Vehicle copyWith({
    String? id,
    String? make,
    String? model,
    int? year,
    String? color,
    String? licensePlate,
    VehicleType? type,
    VehicleStatus? status,
    int? capacity,
    String? image,
    List<String>? features,
    double? pricePerKm,
    double? baseFare,
    String? insuranceNumber,
    DateTime? insuranceExpiry,
    String? registrationNumber,
    DateTime? registrationExpiry,
    DateTime? createdAt,
    DateTime? lastInspectionAt,
  }) {
    return Vehicle(
      id: id ?? this.id,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      color: color ?? this.color,
      licensePlate: licensePlate ?? this.licensePlate,
      type: type ?? this.type,
      status: status ?? this.status,
      capacity: capacity ?? this.capacity,
      image: image ?? this.image,
      features: features ?? this.features,
      pricePerKm: pricePerKm ?? this.pricePerKm,
      baseFare: baseFare ?? this.baseFare,
      insuranceNumber: insuranceNumber ?? this.insuranceNumber,
      insuranceExpiry: insuranceExpiry ?? this.insuranceExpiry,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      registrationExpiry: registrationExpiry ?? this.registrationExpiry,
      createdAt: createdAt ?? this.createdAt,
      lastInspectionAt: lastInspectionAt ?? this.lastInspectionAt,
    );
  }
}
