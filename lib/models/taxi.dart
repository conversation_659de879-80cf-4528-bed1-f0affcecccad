import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/driver.dart';
import 'package:bili_taxi/models/vehicle.dart';

enum TaxiStatus { available, busy, offline, maintenance }

class Taxi {
  final String id;
  final Driver driver;
  final Vehicle vehicle;
  final Location currentLocation;
  final TaxiStatus status;
  final double heading; // Direction en degrés (0-360)
  final double speed; // Vitesse en km/h
  final DateTime lastLocationUpdate;
  final bool isOnline;
  final String? currentRideId;
  final double batteryLevel; // Niveau de batterie du téléphone du conducteur
  final bool gpsEnabled;
  final int signalStrength; // Force du signal (0-100)

  Taxi({
    required this.id,
    required this.driver,
    required this.vehicle,
    required this.currentLocation,
    this.status = TaxiStatus.offline,
    this.heading = 0.0,
    this.speed = 0.0,
    required this.lastLocationUpdate,
    this.isOnline = false,
    this.currentRideId,
    this.batteryLevel = 100.0,
    this.gpsEnabled = true,
    this.signalStrength = 100,
  });

  bool get isAvailable => status == TaxiStatus.available && isOnline;
  bool get isBusy => status == TaxiStatus.busy;
  bool get isOffline => status == TaxiStatus.offline || !isOnline;
  bool get isInMaintenance => status == TaxiStatus.maintenance;

  bool get hasLowBattery => batteryLevel < 20.0;
  bool get hasWeakSignal => signalStrength < 30;
  bool get isLocationStale {
    return DateTime.now().difference(lastLocationUpdate).inMinutes > 5;
  }

  double distanceToLocation(Location location) {
    return currentLocation.distanceTo(location);
  }

  // Estimation du temps d'arrivée en minutes
  int estimatedArrivalTime(Location destination) {
    double distance = distanceToLocation(destination);
    double averageSpeed = speed > 0 ? speed : 30.0; // 30 km/h par défaut
    return ((distance / averageSpeed) * 60).round(); // Conversion en minutes
  }

  factory Taxi.fromJson(Map<String, dynamic> json) {
    return Taxi(
      id: json['id'],
      driver: Driver.fromJson(json['driver']),
      vehicle: Vehicle.fromJson(json['vehicle']),
      currentLocation: Location.fromJson(json['currentLocation']),
      status: TaxiStatus.values.firstWhere(
        (e) => e.toString() == 'TaxiStatus.${json['status']}',
      ),
      heading: json['heading']?.toDouble() ?? 0.0,
      speed: json['speed']?.toDouble() ?? 0.0,
      lastLocationUpdate: DateTime.parse(json['lastLocationUpdate']),
      isOnline: json['isOnline'] ?? false,
      currentRideId: json['currentRideId'],
      batteryLevel: json['batteryLevel']?.toDouble() ?? 100.0,
      gpsEnabled: json['gpsEnabled'] ?? true,
      signalStrength: json['signalStrength'] ?? 100,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'driver': driver.toJson(),
      'vehicle': vehicle.toJson(),
      'currentLocation': currentLocation.toJson(),
      'status': status.toString().split('.').last,
      'heading': heading,
      'speed': speed,
      'lastLocationUpdate': lastLocationUpdate.toIso8601String(),
      'isOnline': isOnline,
      'currentRideId': currentRideId,
      'batteryLevel': batteryLevel,
      'gpsEnabled': gpsEnabled,
      'signalStrength': signalStrength,
    };
  }

  Taxi copyWith({
    String? id,
    Driver? driver,
    Vehicle? vehicle,
    Location? currentLocation,
    TaxiStatus? status,
    double? heading,
    double? speed,
    DateTime? lastLocationUpdate,
    bool? isOnline,
    String? currentRideId,
    double? batteryLevel,
    bool? gpsEnabled,
    int? signalStrength,
  }) {
    return Taxi(
      id: id ?? this.id,
      driver: driver ?? this.driver,
      vehicle: vehicle ?? this.vehicle,
      currentLocation: currentLocation ?? this.currentLocation,
      status: status ?? this.status,
      heading: heading ?? this.heading,
      speed: speed ?? this.speed,
      lastLocationUpdate: lastLocationUpdate ?? this.lastLocationUpdate,
      isOnline: isOnline ?? this.isOnline,
      currentRideId: currentRideId ?? this.currentRideId,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      gpsEnabled: gpsEnabled ?? this.gpsEnabled,
      signalStrength: signalStrength ?? this.signalStrength,
    );
  }

  @override
  String toString() {
    return 'Taxi(id: $id, driver: ${driver.fullName}, status: $status, location: $currentLocation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Taxi && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
