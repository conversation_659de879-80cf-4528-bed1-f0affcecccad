import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:bili_taxi/models/location.dart';

class PlacesPrediction {
  final String placeId;
  final String description;
  final String mainText;
  final String secondaryText;

  PlacesPrediction({
    required this.placeId,
    required this.description,
    required this.mainText,
    required this.secondaryText,
  });

  factory PlacesPrediction.fromJson(Map<String, dynamic> json) {
    return PlacesPrediction(
      placeId: json['place_id'],
      description: json['description'],
      mainText: json['structured_formatting']['main_text'],
      secondaryText: json['structured_formatting']['secondary_text'] ?? '',
    );
  }
}

class PlacesService {
  static const String _apiKey = 'AIzaSyCIYaVLDrGc0Xkvmm0MgN3GbkhfUy74zCQ';
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api/place';

  // Rechercher des lieux avec autocomplétion
  static Future<List<PlacesPrediction>> searchPlaces(String query) async {
    if (query.isEmpty) return [];

    // Pour la démo, retourner des résultats mock si pas de clé API
    if (_apiKey == 'YOUR_GOOGLE_PLACES_API_KEY') {
      return _getMockPredictions(query);
    }

    try {
      final String url = '$_baseUrl/autocomplete/json'
          '?input=${Uri.encodeComponent(query)}'
          '&key=$_apiKey'
          '&language=fr';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final predictions = data['predictions'] as List;

          return predictions
              .map((prediction) => PlacesPrediction.fromJson(prediction))
              .toList();
        }
      }
    } catch (e) {
      print('Erreur lors de la recherche de lieux: $e');
    }

    return _getMockPredictions(query);
  }

  // Obtenir les détails d'un lieu par son place_id
  static Future<Location?> getPlaceDetails(String placeId) async {
    // Pour la démo, retourner des détails mock
    if (_apiKey == 'YOUR_GOOGLE_PLACES_API_KEY') {
      return _getMockLocationDetails(placeId);
    }

    try {
      final String url = '$_baseUrl/details/json'
          '?place_id=$placeId'
          '&key=$_apiKey'
          '&language=fr'
          '&fields=geometry,formatted_address,address_components';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final result = data['result'];
        
        if (result != null) {
          final geometry = result['geometry']['location'];
          final address = result['formatted_address'];
          
          // Extraire ville et pays des composants d'adresse
          String? city;
          String? country;
          String? postalCode;
          
          final addressComponents = result['address_components'] as List?;
          if (addressComponents != null) {
            for (var component in addressComponents) {
              final types = component['types'] as List;
              if (types.contains('locality')) {
                city = component['long_name'];
              } else if (types.contains('country')) {
                country = component['long_name'];
              } else if (types.contains('postal_code')) {
                postalCode = component['long_name'];
              }
            }
          }

          return Location(
            latitude: geometry['lat'].toDouble(),
            longitude: geometry['lng'].toDouble(),
            address: address,
            city: city,
            country: country,
            postalCode: postalCode,
            timestamp: DateTime.now(),
          );
        }
      }
    } catch (e) {
      print('Erreur lors de la récupération des détails: $e');
    }

    return _getMockLocationDetails(placeId);
  }

  // Recherche de lieux à proximité
  static Future<List<PlacesPrediction>> searchNearby(
    double latitude,
    double longitude,
    String type, {
    int radius = 1000,
  }) async {
    // Pour la démo, retourner des résultats mock
    if (_apiKey == 'YOUR_GOOGLE_PLACES_API_KEY') {
      return _getMockNearbyPlaces(type);
    }

    try {
      final String url = '$_baseUrl/nearbysearch/json'
          '?location=$latitude,$longitude'
          '&radius=$radius'
          '&type=$type'
          '&key=$_apiKey'
          '&language=fr';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final results = data['results'] as List;
        
        return results.map((place) {
          return PlacesPrediction(
            placeId: place['place_id'],
            description: place['name'],
            mainText: place['name'],
            secondaryText: place['vicinity'] ?? '',
          );
        }).toList();
      }
    } catch (e) {
      print('Erreur lors de la recherche à proximité: $e');
    }

    return _getMockNearbyPlaces(type);
  }

  // Données mock pour la démo
  static List<PlacesPrediction> _getMockPredictions(String query) {
    final mockPlaces = [
      PlacesPrediction(
        placeId: 'mock_1',
        description: 'Tour Eiffel, Paris, France',
        mainText: 'Tour Eiffel',
        secondaryText: 'Paris, France',
      ),
      PlacesPrediction(
        placeId: 'mock_2',
        description: 'Louvre, Paris, France',
        mainText: 'Musée du Louvre',
        secondaryText: 'Paris, France',
      ),
      PlacesPrediction(
        placeId: 'mock_3',
        description: 'Champs-Élysées, Paris, France',
        mainText: 'Champs-Élysées',
        secondaryText: 'Paris, France',
      ),
      PlacesPrediction(
        placeId: 'mock_4',
        description: 'Notre-Dame, Paris, France',
        mainText: 'Notre-Dame de Paris',
        secondaryText: 'Paris, France',
      ),
      PlacesPrediction(
        placeId: 'mock_5',
        description: 'Arc de Triomphe, Paris, France',
        mainText: 'Arc de Triomphe',
        secondaryText: 'Paris, France',
      ),
    ];

    return mockPlaces
        .where((place) => place.description.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  static Location? _getMockLocationDetails(String placeId) {
    final mockLocations = {
      'mock_1': Location(
        latitude: 48.8584,
        longitude: 2.2945,
        address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris',
        city: 'Paris',
        country: 'France',
        postalCode: '75007',
        timestamp: DateTime.now(),
      ),
      'mock_2': Location(
        latitude: 48.8606,
        longitude: 2.3376,
        address: 'Rue de Rivoli, 75001 Paris',
        city: 'Paris',
        country: 'France',
        postalCode: '75001',
        timestamp: DateTime.now(),
      ),
      'mock_3': Location(
        latitude: 48.8698,
        longitude: 2.3076,
        address: 'Avenue des Champs-Élysées, 75008 Paris',
        city: 'Paris',
        country: 'France',
        postalCode: '75008',
        timestamp: DateTime.now(),
      ),
      'mock_4': Location(
        latitude: 48.8530,
        longitude: 2.3499,
        address: '6 Parvis Notre-Dame - Pl. Jean-Paul II, 75004 Paris',
        city: 'Paris',
        country: 'France',
        postalCode: '75004',
        timestamp: DateTime.now(),
      ),
      'mock_5': Location(
        latitude: 48.8738,
        longitude: 2.2950,
        address: 'Place Charles de Gaulle, 75008 Paris',
        city: 'Paris',
        country: 'France',
        postalCode: '75008',
        timestamp: DateTime.now(),
      ),
    };

    return mockLocations[placeId];
  }

  static List<PlacesPrediction> _getMockNearbyPlaces(String type) {
    return [
      PlacesPrediction(
        placeId: 'nearby_1',
        description: 'Restaurant Le Procope',
        mainText: 'Le Procope',
        secondaryText: 'Restaurant français',
      ),
      PlacesPrediction(
        placeId: 'nearby_2',
        description: 'Hôtel des Invalides',
        mainText: 'Invalides',
        secondaryText: 'Monument historique',
      ),
    ];
  }
}
