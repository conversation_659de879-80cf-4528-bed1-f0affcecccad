import 'dart:async';
import 'package:bili_taxi/models/ride.dart';
import 'package:bili_taxi/viewmodels/user_home_viewmodel.dart';
import 'package:bili_taxi/viewmodels/driver_home_viewmodel.dart';

/// Service de communication entre utilisateur et conducteur
/// Simule les échanges en temps réel pour les demandes de course
class RideCommunicationService {
  static final RideCommunicationService _instance = RideCommunicationService._internal();
  factory RideCommunicationService() => _instance;
  RideCommunicationService._internal();

  // ViewModels connectés
  UserHomeViewModel? _userViewModel;
  DriverHomeViewModel? _driverViewModel;

  // Streams pour la communication
  final StreamController<Ride> _rideRequestController = StreamController<Ride>.broadcast();
  final StreamController<Ride> _rideUpdateController = StreamController<Ride>.broadcast();

  // Getters pour les streams
  Stream<Ride> get rideRequestStream => _rideRequestController.stream;
  Stream<Ride> get rideUpdateStream => _rideUpdateController.stream;

  /// Connecter le ViewModel utilisateur
  void connectUser(UserHomeViewModel userViewModel) {
    _userViewModel = userViewModel;
    
    // Écouter les mises à jour de course
    rideUpdateStream.listen((ride) {
      _userViewModel?._handleRideUpdate(ride);
    });
  }

  /// Connecter le ViewModel conducteur
  void connectDriver(DriverHomeViewModel driverViewModel) {
    _driverViewModel = driverViewModel;
    
    // Écouter les demandes de course
    rideRequestStream.listen((ride) {
      _driverViewModel?.receiveRideRequest(ride);
    });
  }

  /// Envoyer une demande de course (appelé par l'utilisateur)
  void sendRideRequest(Ride ride) {
    _rideRequestController.add(ride);
  }

  /// Envoyer une mise à jour de course (appelé par le conducteur)
  void sendRideUpdate(Ride ride) {
    _rideUpdateController.add(ride);

    // Notifier aussi le conducteur si c'est une réponse utilisateur
    if (ride.status == RideStatus.userAccepted) {
      _driverViewModel?.onUserAccepted();
    } else if (ride.status == RideStatus.userRejected) {
      _driverViewModel?.onUserRejected();
    }
  }

  /// Simuler l'acceptation d'une course par un conducteur
  void simulateDriverAcceptance(Ride ride) {
    final acceptedRide = ride.copyWith(
      status: RideStatus.driverAccepted,
      acceptedAt: DateTime.now(),
    );
    sendRideUpdate(acceptedRide);
  }

  /// Simuler le conducteur en route
  void simulateDriverArriving(Ride ride) {
    final arrivingRide = ride.copyWith(
      status: RideStatus.driverArriving,
    );
    sendRideUpdate(arrivingRide);
  }

  /// Simuler l'arrivée du conducteur
  void simulateDriverArrived(Ride ride) {
    final arrivedRide = ride.copyWith(
      status: RideStatus.driverArrived,
    );
    sendRideUpdate(arrivedRide);
  }

  /// Simuler le démarrage de la course
  void simulateRideStart(Ride ride) {
    final startedRide = ride.copyWith(
      status: RideStatus.inProgress,
      startedAt: DateTime.now(),
    );
    sendRideUpdate(startedRide);
  }

  /// Simuler la fin de la course
  void simulateRideCompletion(Ride ride) {
    final completedRide = ride.copyWith(
      status: RideStatus.completed,
      completedAt: DateTime.now(),
      actualDistance: ride.estimatedDistance,
      actualDuration: ride.estimatedDuration,
      actualFare: ride.estimatedFare,
    );
    sendRideUpdate(completedRide);
  }

  /// Nettoyer les ressources
  void dispose() {
    _rideRequestController.close();
    _rideUpdateController.close();
  }
}

/// Extension pour UserHomeViewModel pour gérer les mises à jour de course
extension UserHomeViewModelCommunication on UserHomeViewModel {
  void _handleRideUpdate(Ride updatedRide) {
    if (currentRide?.id == updatedRide.id) {
      updateCurrentRide(updatedRide);
    }
  }
}
