import 'dart:async';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/taxi.dart';

class MapsService {
  static const String _apiKey = 'AIzaSyCIYaVLDrGc0Xkvmm0MgN3GbkhfUy74zCQ';
  
  static Completer<GoogleMapController>? _controller;
  static GoogleMapController? _mapController;

  // Initialiser le contrôleur de carte
  static void onMapCreated(GoogleMapController controller) {
    if (_controller != null && !_controller!.isCompleted) {
      _controller!.complete(controller);
      _mapController = controller;
    }
  }

  // Obtenir la position actuelle
  static Future<Location> getCurrentLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Pour la démo, retourner Paris si les services sont désactivés
      return Location(
        latitude: 48.8566,
        longitude: 2.3522,
        address: 'Paris, France',
        city: 'Paris',
        country: 'France',
        timestamp: DateTime.now(),
      );
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Retourner Paris par défaut
        return Location(
          latitude: 48.8566,
          longitude: 2.3522,
          address: 'Paris, France',
          city: 'Paris',
          country: 'France',
          timestamp: DateTime.now(),
        );
      }
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      return Location(
        latitude: position.latitude,
        longitude: position.longitude,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      // Fallback vers Paris
      return Location(
        latitude: 48.8566,
        longitude: 2.3522,
        address: 'Paris, France',
        city: 'Paris',
        country: 'France',
        timestamp: DateTime.now(),
      );
    }
  }

  // Stream de position en temps réel
  static Stream<Location> getLocationStream() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    ).map((position) => Location(
      latitude: position.latitude,
      longitude: position.longitude,
      timestamp: DateTime.now(),
    ));
  }

  // Déplacer la caméra vers une position
  static Future<void> moveCamera(Location location, {double zoom = 15.0}) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(location.latitude, location.longitude),
            zoom: zoom,
          ),
        ),
      );
    }
  }

  // Créer des marqueurs pour les taxis
  static Set<Marker> createTaxiMarkers(List<Taxi> taxis, {Function(String)? onTaxiTap}) {
    return taxis.map((taxi) {
      return Marker(
        markerId: MarkerId(taxi.id),
        position: LatLng(
          taxi.currentLocation.latitude,
          taxi.currentLocation.longitude,
        ),
        icon: BitmapDescriptor.defaultMarkerWithHue(
          taxi.isAvailable ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueRed,
        ),
        infoWindow: InfoWindow(
          title: taxi.driver.fullName,
          snippet: 'Note: ${taxi.driver.driverRating.toStringAsFixed(1)} ⭐',
        ),
        onTap: onTaxiTap != null ? () => onTaxiTap(taxi.id) : null,
      );
    }).toSet();
  }

  // Créer un marqueur pour une location
  static Marker createLocationMarker({
    required String id,
    required Location location,
    required String title,
    BitmapDescriptor? icon,
    Function()? onTap,
  }) {
    return Marker(
      markerId: MarkerId(id),
      position: LatLng(location.latitude, location.longitude),
      icon: icon ?? BitmapDescriptor.defaultMarker,
      infoWindow: InfoWindow(
        title: title,
        snippet: location.address,
      ),
      onTap: onTap,
    );
  }

  // Calculer la distance entre deux points
  static double calculateDistance(Location from, Location to) {
    return Geolocator.distanceBetween(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude,
    ) / 1000; // Convertir en kilomètres
  }

  // Obtenir les limites pour afficher plusieurs points
  static LatLngBounds getBounds(List<Location> locations) {
    if (locations.isEmpty) {
      return LatLngBounds(
        southwest: const LatLng(48.8566, 2.3522),
        northeast: const LatLng(48.8566, 2.3522),
      );
    }

    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (Location location in locations) {
      minLat = minLat < location.latitude ? minLat : location.latitude;
      maxLat = maxLat > location.latitude ? maxLat : location.latitude;
      minLng = minLng < location.longitude ? minLng : location.longitude;
      maxLng = maxLng > location.longitude ? maxLng : location.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  // Ajuster la caméra pour afficher tous les points
  static Future<void> fitBounds(LatLngBounds bounds) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 100.0),
      );
    }
  }

  // Nettoyer les ressources
  static void dispose() {
    _controller = null;
    _mapController = null;
  }

  // Initialiser le contrôleur (à appeler dans initState)
  static void initController() {
    _controller = Completer<GoogleMapController>();
  }
}
