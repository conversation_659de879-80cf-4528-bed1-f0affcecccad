import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/models/driver.dart';
import 'package:bili_taxi/models/admin.dart';
import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/vehicle.dart';
import 'package:bili_taxi/models/ride.dart';
import 'package:bili_taxi/models/payment.dart';
import 'package:bili_taxi/models/taxi.dart';

class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal();

  // Utilisateurs mock
  static final User mockUser = User(
    id: 'user_001',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Marie',
    lastName: 'Dupont',
    phone: '+33 6 12 34 56 78',
    profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    userType: UserType.user,
    status: UserStatus.active,
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    lastLoginAt: DateTime.now().subtract(const Duration(hours: 2)),
    currentLocation: Location(
      latitude: 48.8566,
      longitude: 2.3522,
      address: '1 Rue de Rivoli, 75001 Paris',
      city: 'Paris',
      country: 'France',
      postalCode: '75001',
      timestamp: DateTime.now(),
    ),
    rating: 4.8,
    totalRides: 45,
    isEmailVerified: true,
    isPhoneVerified: true,
    fcmToken: 'fcm_token_user_001',
    preferences: {
      'language': 'fr',
      'notifications': true,
      'favoritePaymentMethod': 'card',
    },
  );

  static final Driver mockDriver = Driver(
    id: 'driver_001',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Jean',
    lastName: 'Martin',
    phone: '+33 6 98 76 54 32',
    profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    status: UserStatus.active,
    createdAt: DateTime.now().subtract(const Duration(days: 180)),
    lastLoginAt: DateTime.now().subtract(const Duration(minutes: 15)),
    currentLocation: Location(
      latitude: 48.8606,
      longitude: 2.3376,
      address: 'Place Vendôme, 75001 Paris',
      city: 'Paris',
      country: 'France',
      postalCode: '75001',
      timestamp: DateTime.now(),
    ),
    rating: 4.9,
    totalRides: 1250,
    isEmailVerified: true,
    isPhoneVerified: true,
    fcmToken: 'fcm_token_driver_001',
    licenseNumber: 'FR123456789',
    licenseExpiry: DateTime.now().add(const Duration(days: 365)),
    licenseImage: 'https://example.com/license.jpg',
    vehicle: Vehicle(
      id: 'vehicle_001',
      make: 'Peugeot',
      model: '508',
      year: 2022,
      color: 'Noir',
      licensePlate: 'AB-123-CD',
      type: VehicleType.comfort,
      status: VehicleStatus.active,
      capacity: 4,
      image: 'https://images.unsplash.com/photo-**********-8e3c8b8b8b8b?w=300',
      features: ['Climatisation', 'GPS', 'Bluetooth', 'Chargeur USB'],
      pricePerKm: 1.2,
      baseFare: 3.5,
      insuranceNumber: 'INS123456789',
      insuranceExpiry: DateTime.now().add(const Duration(days: 300)),
      registrationNumber: 'REG123456789',
      registrationExpiry: DateTime.now().add(const Duration(days: 400)),
      createdAt: DateTime.now().subtract(const Duration(days: 150)),
      lastInspectionAt: DateTime.now().subtract(const Duration(days: 90)),
    ),
    driverStatus: DriverStatus.online,
    driverRating: 4.9,
    totalTrips: 1250,
    totalEarnings: 45000.0,
    isVerified: true,
    lastActiveAt: DateTime.now(),
    documents: ['license', 'insurance', 'registration', 'background_check'],
    driverPreferences: {
      'workingHours': {'start': '06:00', 'end': '22:00'},
      'acceptSharedRides': true,
      'maxDistance': 50,
    },
  );

  static final Admin mockAdmin = Admin(
    id: 'admin_001',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Sophie',
    lastName: 'Leroy',
    phone: '+33 1 23 45 67 89',
    profileImage: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150',
    status: UserStatus.active,
    createdAt: DateTime.now().subtract(const Duration(days: 365)),
    lastLoginAt: DateTime.now().subtract(const Duration(minutes: 5)),
    currentLocation: Location(
      latitude: 48.8738,
      longitude: 2.2950,
      address: '16 Avenue Charles de Gaulle, 92200 Neuilly-sur-Seine',
      city: 'Neuilly-sur-Seine',
      country: 'France',
      postalCode: '92200',
      timestamp: DateTime.now(),
    ),
    rating: 5.0,
    totalRides: 0,
    isEmailVerified: true,
    isPhoneVerified: true,
    fcmToken: 'fcm_token_admin_001',
    role: AdminRole.superAdmin,
    permissions: AdminPermission.values, // Toutes les permissions
    department: 'Direction Générale',
    employeeId: 'EMP001',
    isActive: true,
    adminSettings: {
      'dashboardLayout': 'advanced',
      'notifications': {
        'email': true,
        'push': true,
        'sms': false,
      },
      'reportFrequency': 'daily',
    },
  );

  // Taxis disponibles mock
  static List<Taxi> get mockTaxis {
    return [
      Taxi(
        id: 'taxi_001',
        driver: mockDriver,
        vehicle: mockDriver.vehicle!,
        currentLocation: Location(
          latitude: 48.8606,
          longitude: 2.3376,
          address: 'Place Vendôme, 75001 Paris',
          city: 'Paris',
          country: 'France',
          timestamp: DateTime.now(),
        ),
        status: TaxiStatus.available,
        heading: 45.0,
        speed: 0.0,
        lastLocationUpdate: DateTime.now(),
        isOnline: true,
        batteryLevel: 85.0,
        gpsEnabled: true,
        signalStrength: 95,
      ),
      // Taxi 2
      Taxi(
        id: 'taxi_002',
        driver: Driver(
          id: 'driver_002',
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Pierre',
          lastName: 'Bernard',
          phone: '+33 6 87 65 43 21',
          profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
          status: UserStatus.active,
          createdAt: DateTime.now().subtract(const Duration(days: 120)),
          rating: 4.7,
          totalRides: 890,
          isEmailVerified: true,
          isPhoneVerified: true,
          licenseNumber: 'FR987654321',
          licenseExpiry: DateTime.now().add(const Duration(days: 500)),
          vehicle: Vehicle(
            id: 'vehicle_002',
            make: 'Renault',
            model: 'Talisman',
            year: 2021,
            color: 'Blanc',
            licensePlate: 'EF-456-GH',
            type: VehicleType.economy,
            capacity: 4,
            pricePerKm: 1.0,
            baseFare: 3.0,
            createdAt: DateTime.now().subtract(const Duration(days: 100)),
          ),
          driverStatus: DriverStatus.online,
          driverRating: 4.7,
          totalTrips: 890,
          totalEarnings: 32000.0,
          isVerified: true,
        ),
        vehicle: Vehicle(
          id: 'vehicle_002',
          make: 'Renault',
          model: 'Talisman',
          year: 2021,
          color: 'Blanc',
          licensePlate: 'EF-456-GH',
          type: VehicleType.economy,
          capacity: 4,
          pricePerKm: 1.0,
          baseFare: 3.0,
          createdAt: DateTime.now().subtract(const Duration(days: 100)),
        ),
        currentLocation: Location(
          latitude: 48.8534,
          longitude: 2.3488,
          address: 'Rue Saint-Antoine, 75004 Paris',
          city: 'Paris',
          country: 'France',
          timestamp: DateTime.now(),
        ),
        status: TaxiStatus.available,
        heading: 180.0,
        speed: 25.0,
        lastLocationUpdate: DateTime.now().subtract(const Duration(seconds: 30)),
        isOnline: true,
        batteryLevel: 92.0,
        gpsEnabled: true,
        signalStrength: 88,
      ),
    ];
  }

  // Courses mock
  static List<Ride> get mockRides {
    return [
      Ride(
        id: 'ride_001',
        userId: mockUser.id,
        driverId: mockDriver.id,
        pickupLocation: Location(
          latitude: 48.8566,
          longitude: 2.3522,
          address: '1 Rue de Rivoli, 75001 Paris',
          city: 'Paris',
          country: 'France',
        ),
        destinationLocation: Location(
          latitude: 48.8606,
          longitude: 2.3376,
          address: 'Place Vendôme, 75001 Paris',
          city: 'Paris',
          country: 'France',
        ),
        status: RideStatus.completed,
        type: RideType.standard,
        requestedAt: DateTime.now().subtract(const Duration(hours: 2)),
        acceptedAt: DateTime.now().subtract(const Duration(hours: 2, minutes: -2)),
        startedAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 50)),
        completedAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 35)),
        estimatedDistance: 1.2,
        actualDistance: 1.3,
        estimatedDuration: 8,
        actualDuration: 10,
        estimatedFare: 6.5,
        actualFare: 6.8,
        payment: Payment(
          id: 'payment_001',
          rideId: 'ride_001',
          method: PaymentMethod.card,
          status: PaymentStatus.completed,
          amount: 6.8,
          tip: 1.2,
          totalAmount: 8.0,
          currency: 'EUR',
          createdAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 35)),
          processedAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 34)),
          transactionId: 'txn_001',
        ),
        userRating: 5.0,
        driverRating: 5.0,
        userReview: 'Excellent service, conducteur très professionnel!',
        driverReview: 'Passager agréable et ponctuel.',
      ),
    ];
  }

  // Lieux favoris mock
  static List<Location> get mockFavoriteLocations {
    return [
      Location(
        latitude: 48.8566,
        longitude: 2.3522,
        address: 'Domicile - 1 Rue de Rivoli, 75001 Paris',
        city: 'Paris',
        country: 'France',
        postalCode: '75001',
      ),
      Location(
        latitude: 48.8738,
        longitude: 2.2950,
        address: 'Bureau - La Défense, 92200 Neuilly-sur-Seine',
        city: 'Neuilly-sur-Seine',
        country: 'France',
        postalCode: '92200',
      ),
      Location(
        latitude: 48.8584,
        longitude: 2.2945,
        address: 'Aéroport Charles de Gaulle',
        city: 'Roissy-en-France',
        country: 'France',
        postalCode: '95700',
      ),
    ];
  }

  // Méthodes de paiement mock
  static List<Map<String, dynamic>> get mockPaymentMethods {
    return [
      {
        'id': 'pm_001',
        'type': PaymentMethod.card,
        'name': 'Carte Visa ****1234',
        'isDefault': true,
        'icon': 'visa',
      },
      {
        'id': 'pm_002',
        'type': PaymentMethod.cash,
        'name': 'Espèces',
        'isDefault': false,
        'icon': 'cash',
      },
      {
        'id': 'pm_003',
        'type': PaymentMethod.stripe,
        'name': 'Stripe',
        'isDefault': false,
        'icon': 'stripe',
      },
    ];
  }
}
