import 'dart:convert';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:google_polyline_algorithm/google_polyline_algorithm.dart';
import 'package:bili_taxi/models/location.dart';

class DirectionStep {
  final String instruction;
  final double distance;
  final int duration;
  final LatLng startLocation;
  final LatLng endLocation;

  DirectionStep({
    required this.instruction,
    required this.distance,
    required this.duration,
    required this.startLocation,
    required this.endLocation,
  });
}

class DirectionResult {
  final List<LatLng> polylinePoints;
  final double totalDistance;
  final int totalDuration;
  final List<DirectionStep> steps;
  final String summary;

  DirectionResult({
    required this.polylinePoints,
    required this.totalDistance,
    required this.totalDuration,
    required this.steps,
    required this.summary,
  });
}

class DirectionsService {
  static const String _apiKey = 'AIzaSyCIYaVLDrGc0Xkvmm0MgN3GbkhfUy74zCQ';
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api/directions/json';

  // Calculer l'itinéraire entre deux points
  static Future<DirectionResult?> getDirections({
    required Location origin,
    required Location destination,
    String travelMode = 'driving',
  }) async {
    // Pour la démo, retourner un itinéraire mock si pas de clé API
    if (_apiKey == 'YOUR_GOOGLE_DIRECTIONS_API_KEY') {
      return _getMockDirections(origin, destination);
    }

    try {
      final String url = '$_baseUrl'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&mode=$travelMode'
          '&key=$_apiKey'
          '&language=fr';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final leg = route['legs'][0];
          
          // Décoder la polyline
          final polylineString = route['overview_polyline']['points'];
          final polylinePoints = decodePolyline(polylineString)
              .map((point) => LatLng(point[0].toDouble(), point[1].toDouble()))
              .toList();

          // Extraire les étapes
          final steps = <DirectionStep>[];
          for (var step in leg['steps']) {
            steps.add(DirectionStep(
              instruction: step['html_instructions'],
              distance: step['distance']['value'].toDouble(),
              duration: step['duration']['value'],
              startLocation: LatLng(
                step['start_location']['lat'].toDouble(),
                step['start_location']['lng'].toDouble(),
              ),
              endLocation: LatLng(
                step['end_location']['lat'].toDouble(),
                step['end_location']['lng'].toDouble(),
              ),
            ));
          }

          return DirectionResult(
            polylinePoints: polylinePoints,
            totalDistance: leg['distance']['value'].toDouble(),
            totalDuration: leg['duration']['value'],
            steps: steps,
            summary: route['summary'] ?? '',
          );
        }
      }
    } catch (e) {
      // Erreur lors du calcul d'itinéraire: $e
    }

    return _getMockDirections(origin, destination);
  }

  // Calculer l'itinéraire avec des points de passage
  static Future<DirectionResult?> getDirectionsWithWaypoints({
    required Location origin,
    required Location destination,
    required List<Location> waypoints,
    String travelMode = 'driving',
  }) async {
    if (waypoints.isEmpty) {
      return getDirections(origin: origin, destination: destination, travelMode: travelMode);
    }

    // Pour la démo, retourner un itinéraire mock
    if (_apiKey == 'YOUR_GOOGLE_DIRECTIONS_API_KEY') {
      return _getMockDirections(origin, destination);
    }

    try {
      final waypointsString = waypoints
          .map((wp) => '${wp.latitude},${wp.longitude}')
          .join('|');

      final String url = '$_baseUrl'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&waypoints=$waypointsString'
          '&mode=$travelMode'
          '&key=$_apiKey'
          '&language=fr';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          
          // Calculer la distance et durée totales
          double totalDistance = 0;
          int totalDuration = 0;
          final allSteps = <DirectionStep>[];
          
          for (var leg in route['legs']) {
            totalDistance += leg['distance']['value'].toDouble();
            totalDuration += (leg['duration']['value'] as num).toInt();
            
            for (var step in leg['steps']) {
              allSteps.add(DirectionStep(
                instruction: step['html_instructions'],
                distance: step['distance']['value'].toDouble(),
                duration: step['duration']['value'],
                startLocation: LatLng(
                  step['start_location']['lat'].toDouble(),
                  step['start_location']['lng'].toDouble(),
                ),
                endLocation: LatLng(
                  step['end_location']['lat'].toDouble(),
                  step['end_location']['lng'].toDouble(),
                ),
              ));
            }
          }

          // Décoder la polyline
          final polylineString = route['overview_polyline']['points'];
          final polylinePoints = decodePolyline(polylineString)
              .map((point) => LatLng(point[0].toDouble(), point[1].toDouble()))
              .toList();

          return DirectionResult(
            polylinePoints: polylinePoints,
            totalDistance: totalDistance,
            totalDuration: totalDuration,
            steps: allSteps,
            summary: route['summary'] ?? '',
          );
        }
      }
    } catch (e) {
      // Erreur lors du calcul d'itinéraire avec points de passage: $e
    }

    return _getMockDirections(origin, destination);
  }

  // Estimer le temps et la distance
  static Future<Map<String, dynamic>> estimateTimeAndDistance({
    required Location origin,
    required Location destination,
  }) async {
    final directions = await getDirections(origin: origin, destination: destination);
    
    if (directions != null) {
      return {
        'distance': directions.totalDistance / 1000, // en km
        'duration': directions.totalDuration / 60, // en minutes
        'distanceText': '${(directions.totalDistance / 1000).toStringAsFixed(1)} km',
        'durationText': '${(directions.totalDuration / 60).round()} min',
      };
    }

    // Fallback: calcul direct
    final distance = _calculateDirectDistance(origin, destination);
    final estimatedDuration = (distance / 30 * 60).round(); // 30 km/h moyenne

    return {
      'distance': distance,
      'duration': estimatedDuration,
      'distanceText': '${distance.toStringAsFixed(1)} km',
      'durationText': '$estimatedDuration min',
    };
  }

  // Calculer la distance directe entre deux points
  static double _calculateDirectDistance(Location from, Location to) {
    const double earthRadius = 6371; // km
    final dLat = _toRadians(to.latitude - from.latitude);
    final dLon = _toRadians(to.longitude - from.longitude);
    
    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(from.latitude)) * math.cos(_toRadians(to.latitude)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  static double _toRadians(double degree) {
    return degree * (math.pi / 180);
  }

  // Données mock pour la démo
  static DirectionResult _getMockDirections(Location origin, Location destination) {
    // Créer une polyline simple entre les deux points
    final polylinePoints = [
      LatLng(origin.latitude, origin.longitude),
      LatLng(
        (origin.latitude + destination.latitude) / 2,
        (origin.longitude + destination.longitude) / 2,
      ),
      LatLng(destination.latitude, destination.longitude),
    ];

    final distance = _calculateDirectDistance(origin, destination) * 1000; // en mètres
    final duration = (distance / 1000 / 30 * 3600).round(); // 30 km/h en secondes

    return DirectionResult(
      polylinePoints: polylinePoints,
      totalDistance: distance,
      totalDuration: duration,
      steps: [
        DirectionStep(
          instruction: 'Dirigez-vous vers ${destination.address ?? "la destination"}',
          distance: distance,
          duration: duration,
          startLocation: LatLng(origin.latitude, origin.longitude),
          endLocation: LatLng(destination.latitude, destination.longitude),
        ),
      ],
      summary: 'Itinéraire vers ${destination.address ?? "la destination"}',
    );
  }
}


