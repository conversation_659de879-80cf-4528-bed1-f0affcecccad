import 'package:flutter/material.dart';

class AppColors {
  // === COULEURS PRINCIPALES - Design Moderne ===
  static const Color primary = Color(0xFF6366F1);        // Indigo vibrant
  static const Color primaryLight = Color(0xFF818CF8);   // Indigo clair
  static const Color primaryDark = Color(0xFF4F46E5);    // Indigo foncé
  static const Color primarySoft = Color(0xFFEEF2FF);    // Indigo très clair

  static const Color secondary = Color(0xFF10B981);      // Emerald moderne
  static const Color secondaryLight = Color(0xFF34D399); // Emerald clair
  static const Color secondaryDark = Color(0xFF059669);  // Emerald foncé
  static const Color secondarySoft = Color(0xFFECFDF5);  // Emerald très clair

  static const Color accent = Color(0xFFF59E0B);         // Amber élégant
  static const Color accentLight = Color(0xFFFBBF24);    // Amber clair
  static const Color accentDark = Color(0xFFD97706);     // Amber foncé
  static const Color accentSoft = Color(0xFFFEF3C7);     // Amber très clair

  // === COULEURS DE FOND - Hiérarchie claire ===
  static const Color background = Color(0xFFFAFAFA);     // Gris neutre très clair
  static const Color surface = Color(0xFFFFFFFF);        // Blanc pur
  static const Color surfaceVariant = Color(0xFFF8FAFC); // Gris bleuté très clair
  static const Color surfaceElevated = Color(0xFFFFFFFF); // Surface surélevée
  static const Color mapBackground = Color(0xFFF1F5F9);  // Gris bleuté pour carte

  // === COULEURS DE TEXTE - Contraste optimal ===
  static const Color textPrimary = Color(0xFF0F172A);    // Slate 900 - Très lisible
  static const Color textSecondary = Color(0xFF475569);  // Slate 600 - Secondaire
  static const Color textTertiary = Color(0xFF94A3B8);   // Slate 400 - Tertiaire
  static const Color textLight = Color(0xFFFFFFFF);      // Blanc pur
  static const Color textHint = Color(0xFFCBD5E1);       // Slate 300 - Hints

  // === COULEURS D'ÉTAT - Expressives et modernes ===
  static const Color success = Color(0xFF10B981);        // Emerald 500
  static const Color successLight = Color(0xFFD1FAE5);   // Emerald 100
  static const Color successDark = Color(0xFF059669);    // Emerald 700

  static const Color warning = Color(0xFFF59E0B);        // Amber 500
  static const Color warningLight = Color(0xFFFEF3C7);   // Amber 100
  static const Color warningDark = Color(0xFFD97706);    // Amber 700

  static const Color error = Color(0xFFEF4444);          // Red 500
  static const Color errorLight = Color(0xFFFEE2E2);     // Red 100
  static const Color errorDark = Color(0xFFDC2626);      // Red 700

  static const Color info = Color(0xFF3B82F6);           // Blue 500
  static const Color infoLight = Color(0xFFDBEAFE);      // Blue 100
  static const Color infoDark = Color(0xFF2563EB);       // Blue 700

  // === COULEURS NEUTRES - Système complet ===
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF4F4F5);
  static const Color neutral200 = Color(0xFFE4E4E7);
  static const Color neutral300 = Color(0xFFD4D4D8);
  static const Color neutral400 = Color(0xFFA1A1AA);
  static const Color neutral500 = Color(0xFF71717A);
  static const Color neutral600 = Color(0xFF52525B);
  static const Color neutral700 = Color(0xFF3F3F46);
  static const Color neutral800 = Color(0xFF27272A);
  static const Color neutral900 = Color(0xFF18181B);

  // === COULEURS SPÉCIALES ===
  static const Color cardShadow = Color(0x1A000000);     // Ombre douce
  static const Color divider = Color(0xFFE2E8F0);        // Diviseur élégant
  static const Color overlay = Color(0x80000000);        // Overlay modal
  static const Color shimmer = Color(0xFFE2E8F0);        // Effet shimmer

  // === COULEURS TAXI SPÉCIFIQUES ===
  static const Color taxiYellow = Color(0xFFFBBF24);     // Jaune taxi moderne
  static const Color routeColor = Color(0xFF3B82F6);     // Bleu route
  static const Color driverOnline = Color(0xFF10B981);   // Vert en ligne
  static const Color driverOffline = Color(0xFF94A3B8);  // Gris hors ligne
  static const Color driverBusy = Color(0xFFF59E0B);     // Amber occupé

  // === GRADIENTS MODERNES ===
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary],
    stops: [0.0, 1.0],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryLight, secondary],
    stops: [0.0, 1.0],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentLight, accent],
    stops: [0.0, 1.0],
  );

  static const LinearGradient surfaceGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [surface, surfaceVariant],
    stops: [0.0, 1.0],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF34D399), success],
    stops: [0.0, 1.0],
  );

  // === COULEURS VÉHICULES - Design moderne ===
  static const Color economyColor = Color(0xFF10B981);   // Emerald - Économique
  static const Color comfortColor = Color(0xFF3B82F6);   // Blue - Confort
  static const Color premiumColor = Color(0xFF8B5CF6);   // Violet - Premium
  static const Color luxuryColor = Color(0xFFF59E0B);    // Amber - Luxe
  static const Color suvColor = Color(0xFF6B7280);       // Gray - SUV

  // === COULEURS STATUTS COURSE ===
  static const Color rideRequested = Color(0xFFF59E0B);  // Amber - Demandée
  static const Color rideAccepted = Color(0xFF3B82F6);   // Blue - Acceptée
  static const Color rideInProgress = Color(0xFF10B981); // Emerald - En cours
  static const Color rideCompleted = Color(0xFF059669);  // Emerald dark - Terminée
  static const Color rideCancelled = Color(0xFFEF4444);  // Red - Annulée

  // === COULEURS PAIEMENT ===
  static const Color cashColor = Color(0xFF10B981);      // Emerald - Espèces
  static const Color cardColor = Color(0xFF3B82F6);      // Blue - Carte
  static const Color stripeColor = Color(0xFF635BFF);    // Stripe violet
  static const Color paypalColor = Color(0xFF0070BA);    // PayPal bleu
  static const Color applePayColor = Color(0xFF000000);  // Apple Pay noir
  static const Color googlePayColor = Color(0xFF4285F4); // Google Pay bleu

  // === MÉTHODES UTILITAIRES ===
  static Color withAlpha(Color color, int alpha) => color.withAlpha(alpha);

  static Color primaryWithAlpha(int alpha) => primary.withAlpha(alpha);
  static Color secondaryWithAlpha(int alpha) => secondary.withAlpha(alpha);
  static Color successWithAlpha(int alpha) => success.withAlpha(alpha);
  static Color errorWithAlpha(int alpha) => error.withAlpha(alpha);
}
