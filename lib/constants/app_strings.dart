class AppStrings {
  // Nom de l'application
  static const String appName = 'Bili Taxi';
  static const String appSlogan = 'Votre transport en toute simplicité';
  
  // Authentification
  static const String login = 'Connexion';
  static const String register = 'Inscription';
  static const String logout = 'Déconnexion';
  static const String email = 'Email';
  static const String password = 'Mot de passe';
  static const String confirmPassword = 'Confirmer le mot de passe';
  static const String firstName = 'Prénom';
  static const String lastName = 'Nom';
  static const String phone = 'Téléphone';
  static const String forgotPassword = 'Mot de passe oublié ?';
  static const String dontHaveAccount = 'Vous n\'avez pas de compte ?';
  static const String alreadyHaveAccount = 'Vous avez déjà un compte ?';
  static const String signUp = 'S\'inscrire';
  static const String signIn = 'Se connecter';
  
  // Types de compte
  static const String selectAccountType = 'Sélectionnez votre type de compte';
  static const String userAccount = 'Utilisateur';
  static const String driverAccount = 'Conducteur';
  static const String adminAccount = 'Administrateur';
  static const String userDescription = 'Réservez vos courses facilement';
  static const String driverDescription = 'Gagnez de l\'argent en conduisant';
  static const String adminDescription = 'Gérez la plateforme';
  
  // Navigation
  static const String home = 'Accueil';
  static const String map = 'Carte';
  static const String rides = 'Courses';
  static const String profile = 'Profil';
  static const String settings = 'Paramètres';
  static const String history = 'Historique';
  static const String earnings = 'Gains';
  static const String dashboard = 'Tableau de bord';
  
  // Carte et localisation
  static const String whereToGo = 'Où voulez-vous aller ?';
  static const String pickupLocation = 'Lieu de prise en charge';
  static const String destination = 'Destination';
  static const String currentLocation = 'Position actuelle';
  static const String searchLocation = 'Rechercher un lieu...';
  static const String confirmLocation = 'Confirmer la position';
  static const String selectOnMap = 'Sélectionner sur la carte';
  static const String favorites = 'Favoris';
  static const String home_location = 'Domicile';
  static const String work = 'Bureau';
  static const String addToFavorites = 'Ajouter aux favoris';
  
  // Réservation de course
  static const String requestRide = 'Demander une course';
  static const String selectVehicleType = 'Choisir le type de véhicule';
  static const String economy = 'Économique';
  static const String comfort = 'Confort';
  static const String premium = 'Premium';
  static const String luxury = 'Luxe';
  static const String suv = 'SUV';
  static const String estimatedTime = 'Temps estimé';
  static const String estimatedPrice = 'Prix estimé';
  static const String distance = 'Distance';
  static const String duration = 'Durée';
  
  // Statuts de course
  static const String rideRequested = 'Course demandée';
  static const String rideAccepted = 'Course acceptée';
  static const String driverArriving = 'Conducteur en route';
  static const String driverArrived = 'Conducteur arrivé';
  static const String rideInProgress = 'Course en cours';
  static const String rideCompleted = 'Course terminée';
  static const String rideCancelled = 'Course annulée';
  static const String searchingDriver = 'Recherche d\'un conducteur...';
  
  // Paiement
  static const String payment = 'Paiement';
  static const String paymentMethod = 'Méthode de paiement';
  static const String selectPaymentMethod = 'Choisir la méthode de paiement';
  static const String cash = 'Espèces';
  static const String card = 'Carte bancaire';
  static const String stripe = 'Stripe';
  static const String paypal = 'PayPal';
  static const String applePay = 'Apple Pay';
  static const String googlePay = 'Google Pay';
  static const String addPaymentMethod = 'Ajouter une méthode de paiement';
  static const String total = 'Total';
  static const String subtotal = 'Sous-total';
  static const String tip = 'Pourboire';
  static const String discount = 'Remise';
  static const String payNow = 'Payer maintenant';
  
  // Conducteur
  static const String goOnline = 'Se mettre en ligne';
  static const String goOffline = 'Se mettre hors ligne';
  static const String online = 'En ligne';
  static const String offline = 'Hors ligne';
  static const String busy = 'Occupé';
  static const String acceptRide = 'Accepter la course';
  static const String declineRide = 'Refuser la course';
  static const String arrivedAtPickup = 'Arrivé au point de prise en charge';
  static const String startRide = 'Commencer la course';
  static const String completeRide = 'Terminer la course';
  static const String totalEarnings = 'Gains totaux';
  static const String todayEarnings = 'Gains du jour';
  static const String totalTrips = 'Courses totales';
  static const String rating = 'Note';
  
  // Évaluations
  static const String rateYourRide = 'Évaluez votre course';
  static const String rateDriver = 'Évaluer le conducteur';
  static const String ratePassenger = 'Évaluer le passager';
  static const String addReview = 'Ajouter un commentaire';
  static const String submit = 'Envoyer';
  static const String skip = 'Passer';
  
  // Messages d'erreur
  static const String error = 'Erreur';
  static const String success = 'Succès';
  static const String warning = 'Attention';
  static const String info = 'Information';
  static const String invalidEmail = 'Email invalide';
  static const String passwordTooShort = 'Mot de passe trop court';
  static const String passwordsDoNotMatch = 'Les mots de passe ne correspondent pas';
  static const String fieldRequired = 'Ce champ est obligatoire';
  static const String loginFailed = 'Échec de la connexion';
  static const String registrationFailed = 'Échec de l\'inscription';
  static const String networkError = 'Erreur de réseau';
  static const String locationPermissionDenied = 'Permission de localisation refusée';
  static const String noDriversAvailable = 'Aucun conducteur disponible';
  static const String rideCancelledByDriver = 'Course annulée par le conducteur';
  static const String rideCancelledByUser = 'Course annulée par l\'utilisateur';
  
  // Actions générales
  static const String ok = 'OK';
  static const String cancel = 'Annuler';
  static const String save = 'Enregistrer';
  static const String edit = 'Modifier';
  static const String delete = 'Supprimer';
  static const String confirm = 'Confirmer';
  static const String retry = 'Réessayer';
  static const String loading = 'Chargement...';
  static const String refresh = 'Actualiser';
  static const String search = 'Rechercher';
  static const String filter = 'Filtrer';
  static const String sort = 'Trier';
  
  // Profil
  static const String editProfile = 'Modifier le profil';
  static const String changePassword = 'Changer le mot de passe';
  static const String notifications = 'Notifications';
  static const String language = 'Langue';
  static const String helpSupport = 'Aide et support';
  static const String termsConditions = 'Conditions d\'utilisation';
  static const String privacyPolicy = 'Politique de confidentialité';
  static const String aboutApp = 'À propos de l\'application';
  static const String version = 'Version';
  
  // Admin
  static const String userManagement = 'Gestion des utilisateurs';
  static const String driverManagement = 'Gestion des conducteurs';
  static const String rideManagement = 'Gestion des courses';
  static const String reports = 'Rapports';
  static const String analytics = 'Analyses';
  static const String generateReport = 'Générer un rapport';
  static const String generateInvoice = 'Générer une facture';
  static const String totalUsers = 'Utilisateurs totaux';
  static const String totalDrivers = 'Conducteurs totaux';
  static const String totalRides = 'Courses totales';
  static const String revenue = 'Revenus';
  
  // Temps
  static const String today = 'Aujourd\'hui';
  static const String yesterday = 'Hier';
  static const String thisWeek = 'Cette semaine';
  static const String thisMonth = 'Ce mois';
  static const String thisYear = 'Cette année';
  static const String minutes = 'minutes';
  static const String hours = 'heures';
  static const String days = 'jours';
  
  // Unités
  static const String km = 'km';
  static const String meters = 'm';
  static const String euro = '€';
  static const String min = 'min';
}
