import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

enum ModernCardStyle {
  elevated,
  outlined,
  filled,
  glass,
}

class ModernCard extends StatelessWidget {
  final Widget child;
  final ModernCardStyle style;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final List<BoxShadow>? customShadows;

  const ModernCard({
    super.key,
    required this.child,
    this.style = ModernCardStyle.elevated,
    this.padding,
    this.margin,
    this.borderRadius,
    this.onTap,
    this.backgroundColor,
    this.customShadows,
  });

  @override
  Widget build(BuildContext context) {
    Widget card = Container(
      margin: margin,
      decoration: _getDecoration(),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius ?? _getDefaultBorderRadius()),
          child: Padding(
            padding: padding ?? _getDefaultPadding(),
            child: child,
          ),
        ),
      ),
    );

    return card;
  }

  BoxDecoration _getDecoration() {
    switch (style) {
      case ModernCardStyle.elevated:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surface,
          borderRadius: BorderRadius.circular(borderRadius ?? _getDefaultBorderRadius()),
          boxShadow: customShadows ?? _getElevatedShadows(),
        );
      case ModernCardStyle.outlined:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surface,
          borderRadius: BorderRadius.circular(borderRadius ?? _getDefaultBorderRadius()),
          border: Border.all(
            color: AppColors.divider,
            width: 1,
          ),
        );
      case ModernCardStyle.filled:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(borderRadius ?? _getDefaultBorderRadius()),
        );
      case ModernCardStyle.glass:
        return BoxDecoration(
          color: (backgroundColor ?? AppColors.surface).withAlpha(230),
          borderRadius: BorderRadius.circular(borderRadius ?? _getDefaultBorderRadius()),
          border: Border.all(
            color: AppColors.neutral200,
            width: 0.5,
          ),
          boxShadow: _getGlassShadows(),
        );
    }
  }

  double _getDefaultBorderRadius() => 16.0;

  EdgeInsetsGeometry _getDefaultPadding() => const EdgeInsets.all(16.0);

  List<BoxShadow> _getElevatedShadows() {
    return [
      BoxShadow(
        color: AppColors.cardShadow,
        blurRadius: 12,
        offset: const Offset(0, 4),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: AppColors.cardShadow.withAlpha(13),
        blurRadius: 24,
        offset: const Offset(0, 8),
        spreadRadius: 0,
      ),
    ];
  }

  List<BoxShadow> _getGlassShadows() {
    return [
      BoxShadow(
        color: AppColors.cardShadow.withAlpha(25),
        blurRadius: 20,
        offset: const Offset(0, 8),
        spreadRadius: 0,
      ),
    ];
  }
}

// Cartes spécialisées pour l'app taxi
class TaxiCard {
  static Widget driverInfo({
    required String driverName,
    required String vehicleInfo,
    required double rating,
    String? profileImage,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      style: ModernCardStyle.elevated,
      onTap: onTap,
      child: Row(
        children: [
          // Photo du conducteur
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.neutral200,
              image: profileImage != null
                  ? DecorationImage(
                      image: NetworkImage(profileImage),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: profileImage == null
                ? const Icon(
                    Icons.person,
                    color: AppColors.neutral500,
                    size: 28,
                  )
                : null,
          ),
          const SizedBox(width: 16),
          // Informations
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  driverName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  vehicleInfo,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: AppColors.accent,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      rating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.accent,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget rideStatus({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    Widget? action,
  }) {
    return ModernCard(
      style: ModernCardStyle.filled,
      backgroundColor: color.withAlpha(25),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppColors.textLight,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          if (action != null) ...[
            const SizedBox(width: 16),
            action,
          ],
        ],
      ),
    );
  }

  static Widget tripDetails({
    required String pickup,
    required String destination,
    required String distance,
    required String duration,
    required String price,
  }) {
    return ModernCard(
      style: ModernCardStyle.elevated,
      child: Column(
        children: [
          // Trajet
          Row(
            children: [
              Column(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Container(
                    width: 2,
                    height: 24,
                    color: AppColors.neutral300,
                  ),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                      color: AppColors.secondary,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      pickup,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      destination,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Détails
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildDetailItem('Distance', distance, Icons.straighten),
                _buildDetailItem('Durée', duration, Icons.access_time),
                _buildDetailItem('Prix', price, Icons.euro),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildDetailItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.primary,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textTertiary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  // Carte de rapport admin
  static Widget reportCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      style: ModernCardStyle.elevated,
      onTap: onTap,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icône avec fond coloré
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),

          const SizedBox(height: 16),

          // Titre avec gestion du débordement
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 4),

          // Description avec gestion du débordement
          Text(
            description,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
