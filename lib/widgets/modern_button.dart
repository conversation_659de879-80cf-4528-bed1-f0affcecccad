import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';

enum ModernButtonStyle {
  primary,
  secondary,
  outline,
  ghost,
  success,
  warning,
  error,
}

enum ModernButtonSize {
  small,
  medium,
  large,
}

class ModernButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ModernButtonStyle style;
  final ModernButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style = ModernButtonStyle.primary,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: _getHeight(),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: _getButtonStyle(),
        child: isLoading
            ? _buildLoadingIndicator()
            : _buildButtonContent(),
      ),
    );
  }

  double _getHeight() {
    switch (size) {
      case ModernButtonSize.small:
        return 40;
      case ModernButtonSize.medium:
        return 48;
      case ModernButtonSize.large:
        return 56;
    }
  }

  ButtonStyle _getButtonStyle() {
    final colors = _getColors();
    final textStyle = _getTextStyle();

    return ElevatedButton.styleFrom(
      backgroundColor: colors['background'],
      foregroundColor: colors['foreground'],
      elevation: _getElevation(),
      shadowColor: AppColors.cardShadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        side: _getBorderSide(),
      ),
      padding: padding ?? _getPadding(),
      textStyle: textStyle,
    );
  }

  Map<String, Color?> _getColors() {
    switch (style) {
      case ModernButtonStyle.primary:
        return {
          'background': AppColors.primary,
          'foreground': AppColors.textLight,
        };
      case ModernButtonStyle.secondary:
        return {
          'background': AppColors.secondary,
          'foreground': AppColors.textLight,
        };
      case ModernButtonStyle.outline:
        return {
          'background': Colors.transparent,
          'foreground': AppColors.primary,
        };
      case ModernButtonStyle.ghost:
        return {
          'background': Colors.transparent,
          'foreground': AppColors.textPrimary,
        };
      case ModernButtonStyle.success:
        return {
          'background': AppColors.success,
          'foreground': AppColors.textLight,
        };
      case ModernButtonStyle.warning:
        return {
          'background': AppColors.warning,
          'foreground': AppColors.textLight,
        };
      case ModernButtonStyle.error:
        return {
          'background': AppColors.error,
          'foreground': AppColors.textLight,
        };
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case ModernButtonSize.small:
        return AppTypography.buttonSmall;
      case ModernButtonSize.medium:
        return AppTypography.buttonMedium;
      case ModernButtonSize.large:
        return AppTypography.buttonLarge;
    }
  }

  double _getElevation() {
    switch (style) {
      case ModernButtonStyle.outline:
      case ModernButtonStyle.ghost:
        return 0;
      default:
        return 2;
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case ModernButtonSize.small:
        return 8;
      case ModernButtonSize.medium:
        return 12;
      case ModernButtonSize.large:
        return 16;
    }
  }

  BorderSide _getBorderSide() {
    if (style == ModernButtonStyle.outline) {
      return BorderSide(
        color: AppColors.primary,
        width: 1.5,
      );
    }
    return BorderSide.none;
  }

  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case ModernButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ModernButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
      case ModernButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  Widget _buildLoadingIndicator() {
    final colors = _getColors();
    return SizedBox(
      width: 20,
      height: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          colors['foreground'] ?? AppColors.textLight,
        ),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: _getIconSize()),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }
    return Text(text);
  }

  double _getIconSize() {
    switch (size) {
      case ModernButtonSize.small:
        return 16;
      case ModernButtonSize.medium:
        return 18;
      case ModernButtonSize.large:
        return 20;
    }
  }
}

// Boutons spécialisés pour l'app taxi
class TaxiButton {
  static Widget requestRide({
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return ModernButton(
      text: 'Demander une course',
      onPressed: onPressed,
      style: ModernButtonStyle.primary,
      size: ModernButtonSize.large,
      icon: Icons.local_taxi,
      isLoading: isLoading,
      isFullWidth: true,
    );
  }

  static Widget acceptRide({
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return ModernButton(
      text: 'Accepter',
      onPressed: onPressed,
      style: ModernButtonStyle.success,
      size: ModernButtonSize.large,
      icon: Icons.check,
      isLoading: isLoading,
      isFullWidth: true,
    );
  }

  static Widget rejectRide({
    required VoidCallback? onPressed,
  }) {
    return ModernButton(
      text: 'Refuser',
      onPressed: onPressed,
      style: ModernButtonStyle.error,
      size: ModernButtonSize.large,
      icon: Icons.close,
      isFullWidth: true,
    );
  }

  static Widget cancelRide({
    required VoidCallback? onPressed,
  }) {
    return ModernButton(
      text: 'Annuler',
      onPressed: onPressed,
      style: ModernButtonStyle.outline,
      size: ModernButtonSize.medium,
      icon: Icons.cancel_outlined,
    );
  }

  static Widget startTrip({
    required VoidCallback? onPressed,
  }) {
    return ModernButton(
      text: 'Démarrer la course',
      onPressed: onPressed,
      style: ModernButtonStyle.success,
      size: ModernButtonSize.large,
      icon: Icons.play_arrow,
      isFullWidth: true,
    );
  }

  static Widget completeTrip({
    required VoidCallback? onPressed,
  }) {
    return ModernButton(
      text: 'Terminer la course',
      onPressed: onPressed,
      style: ModernButtonStyle.error,
      size: ModernButtonSize.large,
      icon: Icons.stop,
      isFullWidth: true,
    );
  }
}
