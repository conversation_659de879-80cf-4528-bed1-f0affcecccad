import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../constants/app_colors.dart';

enum TaxiButtonStyle {
  primary,
  secondary,
  outline,
  ghost,
  success,
  warning,
  error,
  gradient,
}

enum TaxiButtonSize {
  small,
  medium,
  large,
}

class TaxiButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final TaxiButtonStyle style;
  final TaxiButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const TaxiButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style = TaxiButtonStyle.primary,
    this.size = TaxiButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.padding,
    this.borderRadius,
  });

  // Factory constructors for common button types
  factory TaxiButton.primary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = true,
  }) {
    return TaxiButton(
      text: text,
      onPressed: onPressed,
      style: TaxiButtonStyle.primary,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  factory TaxiButton.gradient({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = true,
  }) {
    return TaxiButton(
      text: text,
      onPressed: onPressed,
      style: TaxiButtonStyle.gradient,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  factory TaxiButton.secondary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = true,
  }) {
    return TaxiButton(
      text: text,
      onPressed: onPressed,
      style: TaxiButtonStyle.secondary,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  factory TaxiButton.outline({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = true,
  }) {
    return TaxiButton(
      text: text,
      onPressed: onPressed,
      style: TaxiButtonStyle.outline,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  factory TaxiButton.requestRide({
    VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return TaxiButton(
      text: 'Demander une course',
      onPressed: onPressed,
      style: TaxiButtonStyle.gradient,
      icon: Icons.local_taxi,
      isLoading: isLoading,
      isFullWidth: true,
      size: TaxiButtonSize.large,
    );
  }

  factory TaxiButton.confirmRide({
    VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return TaxiButton(
      text: 'Confirmer la course',
      onPressed: onPressed,
      style: TaxiButtonStyle.primary,
      icon: Icons.check_circle,
      isLoading: isLoading,
      isFullWidth: true,
      size: TaxiButtonSize.large,
    );
  }

  factory TaxiButton.cancelRide({
    VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return TaxiButton(
      text: 'Annuler',
      onPressed: onPressed,
      style: TaxiButtonStyle.outline,
      icon: Icons.close,
      isLoading: isLoading,
      isFullWidth: true,
    );
  }

  factory TaxiButton.acceptRide({
    VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return TaxiButton(
      text: 'Accepter',
      onPressed: onPressed,
      style: TaxiButtonStyle.success,
      icon: Icons.check,
      isLoading: isLoading,
      isFullWidth: true,
    );
  }

  factory TaxiButton.rejectRide({
    VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return TaxiButton(
      text: 'Refuser',
      onPressed: onPressed,
      style: TaxiButtonStyle.error,
      icon: Icons.close,
      isLoading: isLoading,
      isFullWidth: true,
    );
  }

  @override
  State<TaxiButton> createState() => _TaxiButtonState();
}

class _TaxiButtonState extends State<TaxiButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.isLoading ? null : widget.onPressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        width: widget.isFullWidth ? double.infinity : null,
        height: _getHeight(),
        padding: widget.padding ?? _getPadding(),
        decoration: _getDecoration(),
        child: widget.isLoading
            ? _buildLoadingIndicator()
            : _buildButtonContent(),
      ).animate(target: _isPressed ? 1 : 0)
        .scale(
          begin: const Offset(1.0, 1.0),
          end: const Offset(0.98, 0.98),
          duration: const Duration(milliseconds: 150),
        ),
    );
  }

  double _getHeight() {
    switch (widget.size) {
      case TaxiButtonSize.small:
        return 40;
      case TaxiButtonSize.medium:
        return 50;
      case TaxiButtonSize.large:
        return 56;
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (widget.size) {
      case TaxiButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case TaxiButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
      case TaxiButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  BoxDecoration _getDecoration() {
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(16);
    
    switch (widget.style) {
      case TaxiButtonStyle.primary:
        return BoxDecoration(
          color: AppColors.primary,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );
      
      case TaxiButtonStyle.gradient:
        return BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.rideCancelled,
              AppColors.rideCancelled,
            ],
          ),
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        );
      
      case TaxiButtonStyle.secondary:
        return BoxDecoration(
          color: AppColors.secondary,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.secondary.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );
      
      case TaxiButtonStyle.outline:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: borderRadius,
          border: Border.all(
            color: AppColors.primary,
            width: 2,
          ),
        );
      
      case TaxiButtonStyle.ghost:
        return BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: borderRadius,
        );
      
      case TaxiButtonStyle.success:
        return BoxDecoration(
          color: AppColors.success,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.success.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );
      
      case TaxiButtonStyle.warning:
        return BoxDecoration(
          color: AppColors.warning,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.warning.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );
      
      case TaxiButtonStyle.error:
        return BoxDecoration(
          color: AppColors.error,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: AppColors.error.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );
    }
  }

  Color _getTextColor() {
    switch (widget.style) {
      case TaxiButtonStyle.outline:
        return AppColors.primary;
      case TaxiButtonStyle.ghost:
        return AppColors.primary;
      default:
        return Colors.white;
    }
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
        ),
      ),
    );
  }

  Widget _buildButtonContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            color: _getTextColor(),
            size: 20,
          ),
          const SizedBox(width: 8),
        ],
        Text(
          widget.text,
          style: TextStyle(
            color: _getTextColor(),
            fontSize: _getFontSize(),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  double _getFontSize() {
    switch (widget.size) {
      case TaxiButtonSize.small:
        return 14;
      case TaxiButtonSize.medium:
        return 16;
      case TaxiButtonSize.large:
        return 18;
    }
  }
}
