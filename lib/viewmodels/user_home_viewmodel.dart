import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/vehicle.dart';
import 'package:bili_taxi/models/taxi.dart';
import 'package:bili_taxi/models/ride.dart';
import 'package:bili_taxi/models/payment.dart';
import 'package:bili_taxi/models/driver.dart';
import 'package:bili_taxi/services/maps_service.dart';
import 'package:bili_taxi/services/places_service.dart';
import 'package:bili_taxi/services/directions_service.dart';
import 'package:bili_taxi/services/mock_data_service.dart';
import 'package:bili_taxi/services/ride_communication_service.dart';

class UserHomeViewModel extends ChangeNotifier {
  // État de la carte
  GoogleMapController? _mapController;
  Location? _currentLocation;
  Location? _pickupLocation;
  Location? _destinationLocation;
  
  // État des taxis
  List<Taxi> _availableTaxis = [];
  final Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  
  // État de la recherche
  List<PlacesPrediction> _searchResults = [];
  bool _isSearching = false;
  
  // État de la réservation
  VehicleType _selectedVehicleType = VehicleType.economy;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.card;
  bool _isRequestingRide = false;
  Map<String, dynamic>? _rideEstimation;
  
  // État de la course
  Ride? _currentRide;
  bool _isRideActive = false;

  // Service de communication
  final RideCommunicationService _communicationService = RideCommunicationService();

  // Getters
  GoogleMapController? get mapController => _mapController;
  Location? get currentLocation => _currentLocation;
  Location? get pickupLocation => _pickupLocation;
  Location? get destinationLocation => _destinationLocation;
  List<Taxi> get availableTaxis => _availableTaxis;
  Set<Marker> get markers => _markers;
  Set<Polyline> get polylines => _polylines;
  List<PlacesPrediction> get searchResults => _searchResults;
  bool get isSearching => _isSearching;
  VehicleType get selectedVehicleType => _selectedVehicleType;
  PaymentMethod get selectedPaymentMethod => _selectedPaymentMethod;
  bool get isRequestingRide => _isRequestingRide;
  Map<String, dynamic>? get rideEstimation => _rideEstimation;
  Ride? get currentRide => _currentRide;
  bool get isRideActive => _isRideActive;

  // Getters pour l'état de la course
  bool get isSearchingForDriver => _currentRide?.status == RideStatus.requested;
  bool get isDriverAccepted => _currentRide?.status == RideStatus.driverAccepted;
  bool get isUserAccepted => _currentRide?.status == RideStatus.userAccepted;
  bool get isDriverArriving => _currentRide?.status == RideStatus.driverArriving;
  bool get isDriverArrived => _currentRide?.status == RideStatus.driverArrived;
  bool get isRideInProgress => _currentRide?.status == RideStatus.inProgress;
  bool get isRideCompleted => _currentRide?.status == RideStatus.completed;
  bool get isRideFailed => _currentRide?.status == RideStatus.failed;
  bool get isUserRejected => _currentRide?.status == RideStatus.userRejected;

  // Getter pour les informations du conducteur
  Driver? get assignedDriver {
    if (_currentRide?.driverId != null) {
      return MockDataService.mockDriver; // Pour l'instant, retourner le mock
    }
    return null;
  }

  // Initialisation
  Future<void> initialize() async {
    await _loadCurrentLocation();
    await _loadAvailableTaxis();
    _updateMarkers();

    // Connecter au service de communication
    _communicationService.connectUser(this);
  }

  // Gestion de la carte
  void onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    MapsService.onMapCreated(controller);
    notifyListeners();
  }

  Future<void> _loadCurrentLocation() async {
    try {
      _currentLocation = await MapsService.getCurrentLocation();
      _pickupLocation = _currentLocation;
      notifyListeners();
    } catch (e) {
      // Utiliser la position par défaut de Paris
      _currentLocation = Location(
        latitude: 48.8566,
        longitude: 2.3522,
        address: 'Paris, France',
        city: 'Paris',
        country: 'France',
        timestamp: DateTime.now(),
      );
      _pickupLocation = _currentLocation;
      notifyListeners();
    }
  }

  Future<void> _loadAvailableTaxis() async {
    _availableTaxis = MockDataService.mockTaxis;
    notifyListeners();
  }

  void _updateMarkers() {
    _markers.clear();
    
    // Ajouter les marqueurs des taxis
    _markers.addAll(MapsService.createTaxiMarkers(_availableTaxis));
    
    // Ajouter le marqueur de prise en charge
    if (_pickupLocation != null) {
      _markers.add(MapsService.createLocationMarker(
        id: 'pickup',
        location: _pickupLocation!,
        title: 'Prise en charge',
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      ));
    }
    
    // Ajouter le marqueur de destination
    if (_destinationLocation != null) {
      _markers.add(MapsService.createLocationMarker(
        id: 'destination',
        location: _destinationLocation!,
        title: 'Destination',
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      ));
    }
    
    notifyListeners();
  }

  // Gestion de la recherche de lieux
  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      _searchResults.clear();
      notifyListeners();
      return;
    }

    _isSearching = true;
    notifyListeners();

    try {
      _searchResults = await PlacesService.searchPlaces(query);
    } catch (e) {
      _searchResults = [];
    }

    _isSearching = false;
    notifyListeners();
  }

  Future<void> selectPlace(PlacesPrediction prediction) async {
    try {
      final location = await PlacesService.getPlaceDetails(prediction.placeId);
      if (location != null) {
        setDestination(location);
      }
    } catch (e) {
      // Gérer l'erreur
    }
  }

  // Gestion des locations
  void setPickupLocation(Location location) {
    _pickupLocation = location;
    _updateMarkers();
    _calculateRideEstimation();
  }

  void setDestination(Location location) {
    _destinationLocation = location;
    _updateMarkers();
    _calculateRideEstimation();
    _updateRoute();
  }

  void clearDestination() {
    _destinationLocation = null;
    _rideEstimation = null;
    _updateMarkers();
    _polylines.clear();
    notifyListeners();
  }

  Future<void> _updateRoute() async {
    if (_pickupLocation == null || _destinationLocation == null) {
      _polylines.clear();
      notifyListeners();
      return;
    }

    try {
      final directions = await DirectionsService.getDirections(
        origin: _pickupLocation!,
        destination: _destinationLocation!,
      );

      if (directions != null) {
        _polylines.clear();
        _polylines.add(Polyline(
          polylineId: const PolylineId('route'),
          points: directions.polylinePoints,
          color: Colors.blue,
          width: 4,
        ));
        
        // Ajuster la caméra pour afficher tout l'itinéraire
        if (_mapController != null) {
          final bounds = MapsService.getBounds([_pickupLocation!, _destinationLocation!]);
          await MapsService.fitBounds(bounds);
        }
      }
    } catch (e) {
      // Gérer l'erreur
    }

    notifyListeners();
  }

  Future<void> _calculateRideEstimation() async {
    if (_pickupLocation == null || _destinationLocation == null) {
      _rideEstimation = null;
      notifyListeners();
      return;
    }

    try {
      final estimation = await DirectionsService.estimateTimeAndDistance(
        origin: _pickupLocation!,
        destination: _destinationLocation!,
      );

      // Calculer le prix basé sur le type de véhicule
      final distance = estimation['distance'] as double;
      final baseFare = _getBaseFareForVehicleType(_selectedVehicleType);
      final pricePerKm = _getPricePerKmForVehicleType(_selectedVehicleType);
      final estimatedPrice = baseFare + (distance * pricePerKm);

      _rideEstimation = {
        ...estimation,
        'price': estimatedPrice,
        'priceText': '${estimatedPrice.toStringAsFixed(2)} €',
      };
    } catch (e) {
      _rideEstimation = null;
    }

    notifyListeners();
  }

  double _getBaseFareForVehicleType(VehicleType type) {
    switch (type) {
      case VehicleType.economy:
        return 3.0;
      case VehicleType.comfort:
        return 3.5;
      case VehicleType.premium:
        return 4.0;
      case VehicleType.luxury:
        return 5.0;
      case VehicleType.suv:
        return 4.5;
    }
  }

  double _getPricePerKmForVehicleType(VehicleType type) {
    switch (type) {
      case VehicleType.economy:
        return 1.0;
      case VehicleType.comfort:
        return 1.2;
      case VehicleType.premium:
        return 1.5;
      case VehicleType.luxury:
        return 2.0;
      case VehicleType.suv:
        return 1.3;
    }
  }

  // Gestion de la sélection
  void selectVehicleType(VehicleType type) {
    _selectedVehicleType = type;
    _calculateRideEstimation();
  }

  void selectPaymentMethod(PaymentMethod method) {
    _selectedPaymentMethod = method;
    notifyListeners();
  }

  // Gestion de la réservation
  Future<void> requestRide() async {
    if (_pickupLocation == null || _destinationLocation == null) return;

    _isRequestingRide = true;
    notifyListeners();

    try {
      // Créer une nouvelle course en statut "requested"
      _currentRide = Ride(
        id: 'ride_${DateTime.now().millisecondsSinceEpoch}',
        userId: MockDataService.mockUser.id,
        driverId: null, // Pas encore de conducteur assigné
        pickupLocation: _pickupLocation!,
        destinationLocation: _destinationLocation!,
        status: RideStatus.requested, // ✅ Statut réaliste
        requestedAt: DateTime.now(),
        estimatedDistance: _rideEstimation?['distance'],
        estimatedDuration: _rideEstimation?['duration']?.round(),
        estimatedFare: _rideEstimation?['price'],
      );

      _isRideActive = true;
      _isRequestingRide = false;
      notifyListeners();

      // Envoyer la demande via le service de communication
      _communicationService.sendRideRequest(_currentRide!);

      // Simuler la recherche de conducteur
      await _searchForDriver();
    } catch (e) {
      _isRequestingRide = false;
      _isRideActive = false;
      _currentRide = null;
      notifyListeners();
    }
  }

  // Simulation de recherche de conducteur
  Future<void> _searchForDriver() async {
    if (_currentRide == null) return;

    try {
      // Simuler 5 secondes de recherche
      await Future.delayed(const Duration(seconds: 5));

      // Vérifier si la course n'a pas été annulée
      if (_currentRide?.status != RideStatus.requested) return;

      // Simuler qu'un conducteur accepte
      await _simulateDriverAcceptance();
    } catch (e) {
      // Timeout ou erreur - aucun conducteur trouvé
      await _handleNoDriverFound();
    }
  }

  // Simulation d'acceptation par un conducteur
  Future<void> _simulateDriverAcceptance() async {
    if (_currentRide == null) return;

    // Conducteur accepte la course - attend maintenant l'acceptation utilisateur
    _currentRide = _currentRide!.copyWith(
      status: RideStatus.driverAccepted,
      driverId: MockDataService.mockDriver.id,
      acceptedAt: DateTime.now(),
    );
    notifyListeners();

    // Pas de progression automatique - attend l'action utilisateur
  }

  // Utilisateur accepte le conducteur
  Future<void> acceptDriver() async {
    if (_currentRide?.status != RideStatus.driverAccepted) return;

    _currentRide = _currentRide!.copyWith(
      status: RideStatus.userAccepted,
    );
    notifyListeners();

    // Envoyer la réponse au conducteur
    _communicationService.sendRideUpdate(_currentRide!);

    // Le conducteur va maintenant commencer à venir (géré côté conducteur)
  }

  // Utilisateur refuse le conducteur
  Future<void> rejectDriver() async {
    if (_currentRide?.status != RideStatus.driverAccepted) return;

    _currentRide = _currentRide!.copyWith(
      status: RideStatus.userRejected,
    );
    notifyListeners();

    // Envoyer la réponse au conducteur
    _communicationService.sendRideUpdate(_currentRide!);

    // Attendre 2 secondes puis chercher un autre conducteur
    await Future.delayed(const Duration(seconds: 2));
    if (_currentRide?.status == RideStatus.userRejected) {
      await _searchForAnotherDriver();
    }
  }

  // Chercher un autre conducteur après refus
  Future<void> _searchForAnotherDriver() async {
    if (_currentRide == null) return;

    // Remettre en recherche
    _currentRide = _currentRide!.copyWith(
      status: RideStatus.requested,
      driverId: null,
      acceptedAt: null,
    );
    notifyListeners();

    // Relancer la recherche
    await _searchForDriver();
  }

  // Les simulations de progression sont maintenant gérées côté conducteur
  // L'utilisateur reçoit les mises à jour via le service de communication

  // Gestion du cas où aucun conducteur n'est trouvé
  Future<void> _handleNoDriverFound() async {
    if (_currentRide == null) return;

    _currentRide = _currentRide!.copyWith(
      status: RideStatus.failed,
      cancelledAt: DateTime.now(),
      cancellationReason: 'Aucun conducteur disponible',
    );
    notifyListeners();

    // Attendre 3 secondes puis réinitialiser
    await Future.delayed(const Duration(seconds: 3));
    cancelRide();
  }

  void cancelRide() {
    _currentRide = null;
    _isRideActive = false;
    notifyListeners();
  }

  // Méthode pour mettre à jour la course actuelle (utilisée par le service de communication)
  void updateCurrentRide(Ride updatedRide) {
    _currentRide = updatedRide;
    notifyListeners();
  }

  // Nettoyage
  @override
  void dispose() {
    MapsService.dispose();
    super.dispose();
  }
}
