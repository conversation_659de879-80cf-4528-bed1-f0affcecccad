import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/driver.dart';
import 'package:bili_taxi/models/ride.dart';
import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/services/maps_service.dart';
import 'package:bili_taxi/services/directions_service.dart';
import 'package:bili_taxi/services/mock_data_service.dart';
import 'package:bili_taxi/services/ride_communication_service.dart';

class DriverHomeViewModel extends ChangeNotifier {
  // État de la carte
  GoogleMapController? _mapController;
  Location? _currentLocation;
  
  // État du conducteur
  DriverStatus _driverStatus = DriverStatus.offline;
  Driver _driver = MockDataService.mockDriver;
  
  // État des courses
  Ride? _incomingRide;
  Ride? _currentRide;
  bool _hasIncomingRide = false;
  
  // État de la carte
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  
  // Statistiques du jour
  int _todayTrips = 12;
  double _todayEarnings = 145.0;
  String _todayWorkingTime = '6h 30m';

  // Service de communication
  final RideCommunicationService _communicationService = RideCommunicationService();

  // Getters
  GoogleMapController? get mapController => _mapController;
  Location? get currentLocation => _currentLocation;
  DriverStatus get driverStatus => _driverStatus;
  Driver get driver => _driver;
  Ride? get incomingRide => _incomingRide;
  Ride? get currentRide => _currentRide;
  bool get hasIncomingRide => _hasIncomingRide;
  Set<Marker> get markers => _markers;
  Set<Polyline> get polylines => _polylines;
  int get todayTrips => _todayTrips;
  double get todayEarnings => _todayEarnings;
  String get todayWorkingTime => _todayWorkingTime;

  // Getters de statut
  bool get isOnline => _driverStatus == DriverStatus.online;
  bool get isOffline => _driverStatus == DriverStatus.offline;
  bool get isBusy => _driverStatus == DriverStatus.busy;
  bool get isAvailable => _driverStatus == DriverStatus.online;

  // Getters pour l'état de la course
  bool get isWaitingForUserAcceptance => _currentRide?.status == RideStatus.driverAccepted;
  bool get isUserAccepted => _currentRide?.status == RideStatus.userAccepted;
  bool get canStartTrip => _currentRide?.status == RideStatus.driverArrived;

  // Initialisation
  Future<void> initialize() async {
    await _loadCurrentLocation();
    _updateMarkers();

    // Connecter au service de communication
    _communicationService.connectDriver(this);
  }

  // Gestion de la carte
  void onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    MapsService.onMapCreated(controller);
    notifyListeners();
  }

  Future<void> _loadCurrentLocation() async {
    try {
      _currentLocation = await MapsService.getCurrentLocation();
      notifyListeners();
    } catch (e) {
      // Utiliser la position par défaut du conducteur
      _currentLocation = _driver.currentLocation ?? Location(
        latitude: 48.8606,
        longitude: 2.3376,
        address: 'Paris, France',
        city: 'Paris',
        country: 'France',
        timestamp: DateTime.now(),
      );
      notifyListeners();
    }
  }

  void _updateMarkers() {
    _markers.clear();
    
    // Ajouter le marqueur de position du conducteur
    if (_currentLocation != null) {
      _markers.add(MapsService.createLocationMarker(
        id: 'driver_location',
        location: _currentLocation!,
        title: 'Ma position',
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
      ));
    }
    
    // Ajouter les marqueurs de course si applicable
    if (_incomingRide != null) {
      _markers.add(MapsService.createLocationMarker(
        id: 'pickup',
        location: _incomingRide!.pickupLocation,
        title: 'Prise en charge',
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      ));
      
      _markers.add(MapsService.createLocationMarker(
        id: 'destination',
        location: _incomingRide!.destinationLocation,
        title: 'Destination',
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      ));
    }
    
    notifyListeners();
  }

  // Gestion du statut du conducteur
  void toggleDriverStatus() {
    if (_driverStatus == DriverStatus.offline) {
      _driverStatus = DriverStatus.online;
      // Simuler une demande de course après 3 secondes
      Future.delayed(const Duration(seconds: 3), () {
        if (_driverStatus == DriverStatus.online) {
          _simulateIncomingRide();
        }
      });
    } else if (_driverStatus == DriverStatus.online) {
      _driverStatus = DriverStatus.offline;
      _hasIncomingRide = false;
      _incomingRide = null;
    }
    notifyListeners();
  }

  void _simulateIncomingRide() {
    _incomingRide = Ride(
      id: 'ride_${DateTime.now().millisecondsSinceEpoch}',
      userId: MockDataService.mockUser.id,
      pickupLocation: Location(
        latitude: 48.8566,
        longitude: 2.3522,
        address: '1 Rue de Rivoli, 75001 Paris',
        city: 'Paris',
        country: 'France',
      ),
      destinationLocation: Location(
        latitude: 48.8606,
        longitude: 2.3376,
        address: 'Place Vendôme, 75001 Paris',
        city: 'Paris',
        country: 'France',
      ),
      status: RideStatus.requested,
      requestedAt: DateTime.now(),
      estimatedDistance: 1.2,
      estimatedDuration: 8,
      estimatedFare: 6.5,
    );
    
    _hasIncomingRide = true;
    _updateMarkers();
    _updateRoute();
    notifyListeners();
  }

  // Recevoir une vraie demande de course (sera appelé par le système de communication)
  void receiveRideRequest(Ride rideRequest) {
    if (_driverStatus != DriverStatus.online) return;

    _incomingRide = rideRequest;
    _hasIncomingRide = true;
    _updateMarkers();
    _updateRoute();
    notifyListeners();
  }

  // Gestion des courses
  void acceptRide() {
    if (_incomingRide != null) {
      _currentRide = _incomingRide!.copyWith(
        status: RideStatus.driverAccepted, // Attend maintenant l'acceptation utilisateur
        driverId: _driver.id,
        acceptedAt: DateTime.now(),
      );
      _driverStatus = DriverStatus.busy;
      _hasIncomingRide = false;
      _incomingRide = null;
      notifyListeners();

      // Envoyer la mise à jour via le service de communication
      _communicationService.sendRideUpdate(_currentRide!);
    }
  }

  void declineRide() {
    _incomingRide = null;
    _hasIncomingRide = false;
    _updateMarkers();
    notifyListeners();
  }

  // Gérer l'acceptation de l'utilisateur
  void onUserAccepted() {
    if (_currentRide?.status == RideStatus.driverAccepted) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.userAccepted,
      );
      notifyListeners();

      // Maintenant le conducteur doit manuellement cliquer "Aller au pickup"
      // Pas de progression automatique
    }
  }

  // Gérer le refus de l'utilisateur
  void onUserRejected() {
    if (_currentRide?.status == RideStatus.driverAccepted) {
      // Remettre le conducteur disponible
      _currentRide = null;
      _driverStatus = DriverStatus.online;
      notifyListeners();
    }
  }

  // Conducteur clique "Aller au pickup"
  void goToPickup() {
    if (_currentRide?.status == RideStatus.userAccepted) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.driverArriving,
      );
      notifyListeners();

      // Envoyer la mise à jour à l'utilisateur
      _communicationService.sendRideUpdate(_currentRide!);
    }
  }

  // Conducteur clique "Je suis arrivé"
  void arriveAtPickup() {
    if (_currentRide?.status == RideStatus.driverArriving) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.driverArrived,
      );
      notifyListeners();

      // Envoyer la mise à jour à l'utilisateur
      _communicationService.sendRideUpdate(_currentRide!);
    }
  }

  // Conducteur clique "Démarrer la course"
  void startTrip() {
    if (_currentRide?.status == RideStatus.driverArrived) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.inProgress,
        startedAt: DateTime.now(),
      );
      notifyListeners();

      // Envoyer la mise à jour à l'utilisateur
      _communicationService.sendRideUpdate(_currentRide!);

      // Pas de simulation automatique - le conducteur contrôle
    }
  }

  // Conducteur clique "Terminer la course"
  void completeTrip() {
    if (_currentRide?.status == RideStatus.inProgress) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.completed,
        completedAt: DateTime.now(),
        actualDistance: _currentRide!.estimatedDistance,
        actualDuration: _currentRide!.estimatedDuration,
        actualFare: _currentRide!.estimatedFare,
      );

      // Mettre à jour les statistiques
      _todayTrips++;
      _todayEarnings += _currentRide!.actualFare ?? 0;

      // Remettre le conducteur disponible
      _driverStatus = DriverStatus.online;

      notifyListeners();

      // Envoyer la mise à jour à l'utilisateur
      _communicationService.sendRideUpdate(_currentRide!);

      // Réinitialiser après 3 secondes
      Timer(const Duration(seconds: 3), () {
        _currentRide = null;
        notifyListeners();
      });
    }
  }

  void startRide() {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.inProgress,
        startedAt: DateTime.now(),
      );
      notifyListeners();
    }
  }

  void completeRide() {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.completed,
        completedAt: DateTime.now(),
        actualDistance: _currentRide!.estimatedDistance,
        actualDuration: _currentRide!.estimatedDuration,
        actualFare: _currentRide!.estimatedFare,
      );
      
      // Mettre à jour les statistiques
      _todayTrips++;
      _todayEarnings += _currentRide!.actualFare ?? 0;
      
      // Remettre le conducteur en ligne
      _driverStatus = DriverStatus.online;
      _currentRide = null;
      
      notifyListeners();
    }
  }

  Future<void> _updateRoute() async {
    if (_incomingRide == null || _currentLocation == null) {
      _polylines.clear();
      notifyListeners();
      return;
    }

    try {
      final directions = await DirectionsService.getDirections(
        origin: _currentLocation!,
        destination: _incomingRide!.pickupLocation,
      );

      if (directions != null) {
        _polylines.clear();
        _polylines.add(Polyline(
          polylineId: const PolylineId('route_to_pickup'),
          points: directions.polylinePoints,
          color: Colors.blue,
          width: 4,
        ));
        
        // Ajuster la caméra pour afficher tout l'itinéraire
        if (_mapController != null) {
          final bounds = MapsService.getBounds([_currentLocation!, _incomingRide!.pickupLocation]);
          await MapsService.fitBounds(bounds);
        }
      }
    } catch (e) {
      // Gérer l'erreur
    }

    notifyListeners();
  }

  // Obtenir la couleur du statut
  Color getStatusColor() {
    switch (_driverStatus) {
      case DriverStatus.online:
        return const Color(0xFF4CAF50); // Vert
      case DriverStatus.busy:
        return const Color(0xFFFF9800); // Orange
      case DriverStatus.offline:
      case DriverStatus.unavailable:
        return const Color(0xFF9E9E9E); // Gris
    }
  }

  // Obtenir le texte du statut
  String getStatusText() {
    switch (_driverStatus) {
      case DriverStatus.online:
        return 'En ligne - Prêt à recevoir des courses';
      case DriverStatus.busy:
        return 'Occupé - Course en cours';
      case DriverStatus.offline:
        return 'Hors ligne - Appuyez pour vous mettre en ligne';
      case DriverStatus.unavailable:
        return 'Indisponible';
    }
  }

  // Obtenir les gains d'un jour spécifique (simulation)
  Map<String, dynamic> getDayEarnings(DateTime date) {
    final daysDiff = DateTime.now().difference(date).inDays;
    final earnings = (100 + (daysDiff * 20)) + (daysDiff % 3 * 15);
    final trips = 8 + (daysDiff % 3);
    
    return {
      'earnings': earnings,
      'trips': trips,
      'date': date,
    };
  }

  // Nettoyage
  @override
  void dispose() {
    MapsService.dispose();
    super.dispose();
  }
}
