import 'package:flutter/material.dart';
import 'package:bili_taxi/models/admin.dart';
import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/models/driver.dart';
import 'package:bili_taxi/models/ride.dart';
import 'package:bili_taxi/services/mock_data_service.dart';

class AdminDashboardViewModel extends ChangeNotifier {
  // État de l'admin
  Admin _admin = MockDataService.mockAdmin;
  
  // Statistiques principales
  int _totalUsers = 1234;
  int _totalDrivers = 456;
  int _totalRides = 12567;
  double _totalRevenue = 45890.0;
  
  // Tendances (pourcentages)
  String _usersTrend = '+12%';
  String _driversTrend = '+8%';
  String _ridesTrend = '+15%';
  String _revenueTrend = '+22%';
  
  // Activités récentes
  List<Map<String, dynamic>> _recentActivities = [];
  
  // État de la recherche d'utilisateurs
  List<Map<String, dynamic>> _users = [];
  List<Map<String, dynamic>> _filteredUsers = [];
  String _searchQuery = '';
  
  // État des rapports
  bool _isGeneratingReport = false;

  // Getters
  Admin get admin => _admin;
  int get totalUsers => _totalUsers;
  int get totalDrivers => _totalDrivers;
  int get totalRides => _totalRides;
  double get totalRevenue => _totalRevenue;
  String get usersTrend => _usersTrend;
  String get driversTrend => _driversTrend;
  String get ridesTrend => _ridesTrend;
  String get revenueTrend => _revenueTrend;
  List<Map<String, dynamic>> get recentActivities => _recentActivities;
  List<Map<String, dynamic>> get users => _users;
  List<Map<String, dynamic>> get filteredUsers => _filteredUsers;
  String get searchQuery => _searchQuery;
  bool get isGeneratingReport => _isGeneratingReport;

  // Initialisation
  Future<void> initialize() async {
    await _loadStatistics();
    await _loadRecentActivities();
    await _loadUsers();
  }

  Future<void> _loadStatistics() async {
    // Simulation du chargement des statistiques
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Les statistiques sont déjà initialisées
    notifyListeners();
  }

  Future<void> _loadRecentActivities() async {
    _recentActivities = [
      {
        'icon': Icons.person_add,
        'title': 'Nouvel utilisateur inscrit',
        'subtitle': 'Marie Dubois - il y a 5 minutes',
        'color': const Color(0xFF2E7D32),
        'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
      },
      {
        'icon': Icons.car_rental,
        'title': 'Nouveau conducteur approuvé',
        'subtitle': 'Pierre Martin - il y a 12 minutes',
        'color': const Color(0xFFFF9800),
        'timestamp': DateTime.now().subtract(const Duration(minutes: 12)),
      },
      {
        'icon': Icons.local_taxi,
        'title': 'Course terminée',
        'subtitle': 'Course #12567 - €15.50',
        'color': const Color(0xFF4CAF50),
        'timestamp': DateTime.now().subtract(const Duration(minutes: 18)),
      },
      {
        'icon': Icons.warning,
        'title': 'Signalement utilisateur',
        'subtitle': 'Nécessite une attention',
        'color': const Color(0xFFFF9800),
        'timestamp': DateTime.now().subtract(const Duration(minutes: 25)),
      },
    ];
    notifyListeners();
  }

  Future<void> _loadUsers() async {
    // Simulation du chargement des utilisateurs
    _users = List.generate(50, (index) {
      return {
        'id': 'user_${index + 1}',
        'name': 'Utilisateur ${index + 1}',
        'email': 'user${index + 1}@email.com',
        'status': index % 10 == 0 ? 'Suspendu' : 'Actif',
        'type': index % 5 == 0 ? 'Conducteur' : 'Utilisateur',
        'joinDate': DateTime.now().subtract(Duration(days: index * 2)),
        'totalRides': (index + 1) * 3,
        'rating': 4.0 + (index % 10) * 0.1,
      };
    });
    
    _filteredUsers = List.from(_users);
    notifyListeners();
  }

  // Gestion de la recherche d'utilisateurs
  void searchUsers(String query) {
    _searchQuery = query;
    
    if (query.isEmpty) {
      _filteredUsers = List.from(_users);
    } else {
      _filteredUsers = _users.where((user) {
        return user['name'].toLowerCase().contains(query.toLowerCase()) ||
               user['email'].toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
    
    notifyListeners();
  }

  // Actions sur les utilisateurs
  void suspendUser(String userId) {
    final userIndex = _users.indexWhere((user) => user['id'] == userId);
    if (userIndex != -1) {
      _users[userIndex]['status'] = 'Suspendu';
      searchUsers(_searchQuery); // Rafraîchir la liste filtrée
    }
  }

  void activateUser(String userId) {
    final userIndex = _users.indexWhere((user) => user['id'] == userId);
    if (userIndex != -1) {
      _users[userIndex]['status'] = 'Actif';
      searchUsers(_searchQuery); // Rafraîchir la liste filtrée
    }
  }

  void deleteUser(String userId) {
    _users.removeWhere((user) => user['id'] == userId);
    searchUsers(_searchQuery); // Rafraîchir la liste filtrée
    _totalUsers--;
    notifyListeners();
  }

  // Génération de rapports
  Future<void> generateReport(String reportType) async {
    _isGeneratingReport = true;
    notifyListeners();

    // Simulation de la génération de rapport
    await Future.delayed(const Duration(seconds: 2));

    _isGeneratingReport = false;
    notifyListeners();

    // Retourner un message de succès
    _addRecentActivity(
      icon: Icons.assessment,
      title: 'Rapport généré',
      subtitle: 'Rapport $reportType généré avec succès',
      color: const Color(0xFF2196F3),
    );
  }

  Future<void> generateFinancialReport() async {
    await generateReport('financier');
  }

  Future<void> generateUserReport() async {
    await generateReport('utilisateurs');
  }

  Future<void> generateDriverReport() async {
    await generateReport('conducteurs');
  }

  Future<void> generateRideReport() async {
    await generateReport('courses');
  }

  Future<void> generateInvoice() async {
    _isGeneratingReport = true;
    notifyListeners();

    // Simulation de la génération de facture
    await Future.delayed(const Duration(seconds: 2));

    _isGeneratingReport = false;
    notifyListeners();

    _addRecentActivity(
      icon: Icons.receipt,
      title: 'Facture générée',
      subtitle: 'Facture #${DateTime.now().millisecondsSinceEpoch} créée',
      color: const Color(0xFF4CAF50),
    );
  }

  // Ajouter une activité récente
  void _addRecentActivity({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    _recentActivities.insert(0, {
      'icon': icon,
      'title': title,
      'subtitle': subtitle,
      'color': color,
      'timestamp': DateTime.now(),
    });

    // Garder seulement les 10 dernières activités
    if (_recentActivities.length > 10) {
      _recentActivities = _recentActivities.take(10).toList();
    }

    notifyListeners();
  }

  // Obtenir les statistiques pour une période
  Map<String, dynamic> getStatisticsForPeriod(String period) {
    // Simulation de données pour différentes périodes
    switch (period) {
      case 'today':
        return {
          'users': 45,
          'drivers': 12,
          'rides': 234,
          'revenue': 1250.0,
        };
      case 'week':
        return {
          'users': 312,
          'drivers': 89,
          'rides': 1567,
          'revenue': 8900.0,
        };
      case 'month':
        return {
          'users': _totalUsers,
          'drivers': _totalDrivers,
          'rides': _totalRides,
          'revenue': _totalRevenue,
        };
      default:
        return {
          'users': _totalUsers,
          'drivers': _totalDrivers,
          'rides': _totalRides,
          'revenue': _totalRevenue,
        };
    }
  }

  // Obtenir les données pour les graphiques
  List<Map<String, dynamic>> getChartData(String type) {
    // Simulation de données pour les graphiques
    return List.generate(7, (index) {
      final date = DateTime.now().subtract(Duration(days: 6 - index));
      return {
        'date': date,
        'value': (index + 1) * 100 + (type.hashCode % 50),
      };
    });
  }

  // Rafraîchir les données
  Future<void> refresh() async {
    await _loadStatistics();
    await _loadRecentActivities();
    await _loadUsers();
  }
}
