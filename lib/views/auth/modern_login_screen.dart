import 'package:flutter/material.dart';
import 'package:bili_taxi/constants/app_colors.dart';
import 'package:bili_taxi/constants/app_strings.dart';
import 'package:bili_taxi/constants/app_typography.dart';
import 'package:bili_taxi/widgets/modern_button.dart';
import 'package:bili_taxi/widgets/modern_card.dart';
import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/services/mock_data_service.dart';
import 'package:bili_taxi/views/auth/register_screen.dart';
import 'package:bili_taxi/views/user/user_home_screen.dart';
import 'package:bili_taxi/views/driver/driver_home_screen.dart';
import 'package:bili_taxi/views/admin/admin_dashboard_screen.dart';

class ModernLoginScreen extends StatefulWidget {
  final UserType userType;
  
  const ModernLoginScreen({super.key, required this.userType});

  @override
  State<ModernLoginScreen> createState() => _ModernLoginScreenState();
}

class _ModernLoginScreenState extends State<ModernLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: SizedBox(
            height: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            child: Column(
              children: [
                // Header moderne avec gradient
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(32),
                      bottomRight: Radius.circular(32),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        // Bouton retour
                        Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.arrow_back, color: AppColors.textLight),
                              onPressed: () => Navigator.pop(context),
                            ),
                            const Spacer(),
                          ],
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Icône et titre
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.textLight.withAlpha(25),
                            borderRadius: BorderRadius.circular(50),
                            border: Border.all(
                              color: AppColors.textLight.withAlpha(76),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            _getIcon(),
                            size: 50,
                            color: AppColors.textLight,
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        Text(
                          _getTitle(),
                          style: AppTypography.displayMedium.copyWith(
                            color: AppColors.textLight,
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Text(
                          _getSubtitle(),
                          style: AppTypography.bodyLarge.copyWith(
                            color: AppColors.textLight.withAlpha(204),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
                
                // Formulaire de connexion
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          const SizedBox(height: 32),
                          
                          // Champ email moderne
                          _buildModernTextField(
                            controller: _emailController,
                            label: AppStrings.email,
                            hint: '<EMAIL>',
                            icon: Icons.email_outlined,
                            keyboardType: TextInputType.emailAddress,
                            validator: _validateEmail,
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // Champ mot de passe moderne
                          _buildModernTextField(
                            controller: _passwordController,
                            label: AppStrings.password,
                            hint: 'Votre mot de passe',
                            icon: Icons.lock_outline,
                            isPassword: true,
                            validator: _validatePassword,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Mot de passe oublié
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: () {
                                // TODO: Implémenter mot de passe oublié
                              },
                              child: Text(
                                'Mot de passe oublié ?',
                                style: AppTypography.labelMedium.copyWith(
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Bouton de connexion moderne
                          ModernButton(
                            text: AppStrings.login,
                            onPressed: _isLoading ? null : _handleLogin,
                            style: ModernButtonStyle.primary,
                            size: ModernButtonSize.large,
                            icon: Icons.login,
                            isLoading: _isLoading,
                            isFullWidth: true,
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Divider "ou"
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: 1,
                                  color: AppColors.divider,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: Text(
                                  'ou',
                                  style: AppTypography.labelMedium.copyWith(
                                    color: AppColors.textTertiary,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  height: 1,
                                  color: AppColors.divider,
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Bouton d'inscription
                          ModernButton(
                            text: 'Créer un compte',
                            onPressed: () => _navigateToRegister(),
                            style: ModernButtonStyle.outline,
                            size: ModernButtonSize.large,
                            icon: Icons.person_add,
                            isFullWidth: true,
                          ),
                          
                          const Spacer(),
                          
                          // Informations de test
                          if (widget.userType != UserType.user) ...[
                            ModernCard(
                              style: ModernCardStyle.filled,
                              backgroundColor: AppColors.infoLight,
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: AppColors.info,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Compte de test',
                                        style: AppTypography.labelLarge.copyWith(
                                          color: AppColors.info,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Email: ${_getTestEmail()}\nMot de passe: password123',
                                    style: AppTypography.bodySmall.copyWith(
                                      color: AppColors.infoDark,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool isPassword = false,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: isPassword && !_isPasswordVisible,
          validator: validator,
          style: AppTypography.bodyLarge,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTypography.bodyLarge.copyWith(
              color: AppColors.textTertiary,
            ),
            prefixIcon: Icon(
              icon,
              color: AppColors.textSecondary,
            ),
            suffixIcon: isPassword
                ? IconButton(
                    icon: Icon(
                      _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  )
                : null,
            filled: true,
            fillColor: AppColors.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.divider),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.divider),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.error),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
      ],
    );
  }

  // Méthodes utilitaires
  IconData _getIcon() {
    switch (widget.userType) {
      case UserType.user:
        return Icons.person;
      case UserType.driver:
        return Icons.local_taxi;
      case UserType.admin:
        return Icons.admin_panel_settings;
    }
  }

  String _getTitle() {
    switch (widget.userType) {
      case UserType.user:
        return 'Connexion Client';
      case UserType.driver:
        return 'Connexion Conducteur';
      case UserType.admin:
        return 'Connexion Admin';
    }
  }

  String _getSubtitle() {
    switch (widget.userType) {
      case UserType.user:
        return 'Connectez-vous pour réserver vos courses';
      case UserType.driver:
        return 'Connectez-vous pour commencer à conduire';
      case UserType.admin:
        return 'Accédez au tableau de bord administrateur';
    }
  }

  String _getTestEmail() {
    switch (widget.userType) {
      case UserType.user:
        return '<EMAIL>';
      case UserType.driver:
        return '<EMAIL>';
      case UserType.admin:
        return '<EMAIL>';
    }
  }

  // Validation
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Veuillez saisir votre email';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Veuillez saisir un email valide';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Veuillez saisir votre mot de passe';
    }
    if (value.length < 6) {
      return 'Le mot de passe doit contenir au moins 6 caractères';
    }
    return null;
  }

  // Actions
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulation de connexion
      await Future.delayed(const Duration(seconds: 2));

      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // Vérification des identifiants de test
      bool isValidLogin = false;
      switch (widget.userType) {
        case UserType.user:
          isValidLogin = email == '<EMAIL>' && password == 'password123';
          break;
        case UserType.driver:
          isValidLogin = email == '<EMAIL>' && password == 'password123';
          break;
        case UserType.admin:
          isValidLogin = email == '<EMAIL>' && password == 'password123';
          break;
      }

      if (isValidLogin) {
        _navigateToHome();
      } else {
        _showErrorSnackBar('Email ou mot de passe incorrect');
      }
    } catch (e) {
      _showErrorSnackBar('Erreur de connexion');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToHome() {
    switch (widget.userType) {
      case UserType.user:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const UserHomeScreen()),
        );
        break;
      case UserType.driver:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const DriverHomeScreen()),
        );
        break;
      case UserType.admin:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const AdminDashboardScreen()),
        );
        break;
    }
  }

  void _navigateToRegister() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegisterScreen(userType: widget.userType),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
