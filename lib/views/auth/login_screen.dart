import 'package:flutter/material.dart';
import 'package:bili_taxi/constants/app_colors.dart';
import 'package:bili_taxi/constants/app_strings.dart';
import 'package:bili_taxi/constants/app_typography.dart';
import 'package:bili_taxi/widgets/modern_button.dart';
import 'package:bili_taxi/widgets/modern_card.dart';
import 'package:bili_taxi/models/user.dart';
import 'package:bili_taxi/services/mock_data_service.dart';
import 'package:bili_taxi/views/auth/register_screen.dart';
import 'package:bili_taxi/views/user/user_home_screen.dart';
import 'package:bili_taxi/views/driver/driver_home_screen.dart';
import 'package:bili_taxi/views/admin/admin_dashboard_screen.dart';

class LoginScreen extends StatefulWidget {
  final UserType userType;
  
  const LoginScreen({Key? key, required this.userType}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                
                // Titre et sous-titre
                Text(
                  AppStrings.login,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getSubtitle(),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 40),
                
                // Champ email
                _buildTextField(
                  controller: _emailController,
                  label: AppStrings.email,
                  hint: '<EMAIL>',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  validator: _validateEmail,
                ),
                const SizedBox(height: 20),
                
                // Champ mot de passe
                _buildTextField(
                  controller: _passwordController,
                  label: AppStrings.password,
                  hint: 'Votre mot de passe',
                  icon: Icons.lock_outline,
                  isPassword: true,
                  validator: _validatePassword,
                ),
                const SizedBox(height: 16),
                
                // Mot de passe oublié
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: _onForgotPassword,
                    child: Text(
                      AppStrings.forgotPassword,
                      style: TextStyle(
                        color: _getThemeColor(),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                
                // Bouton de connexion
                Container(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _onLogin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getThemeColor(),
                      foregroundColor: AppColors.textLight,
                      elevation: 8,
                      shadowColor: _getThemeColor().withOpacity(0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(
                            color: AppColors.textLight,
                          )
                        : Text(
                            AppStrings.signIn,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppColors.textLight,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 32),
                
                // Divider
                Row(
                  children: [
                    Expanded(child: Divider(color: AppColors.textHint.withOpacity(0.5))),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'ou',
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                    ),
                    Expanded(child: Divider(color: AppColors.textHint.withOpacity(0.5))),
                  ],
                ),
                const SizedBox(height: 32),
                
                // Lien vers inscription
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppStrings.dontHaveAccount,
                      style: TextStyle(color: AppColors.textSecondary),
                    ),
                    TextButton(
                      onPressed: _onGoToRegister,
                      child: Text(
                        AppStrings.signUp,
                        style: TextStyle(
                          color: _getThemeColor(),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Informations de test
                if (widget.userType != UserType.admin) ...[
                  const SizedBox(height: 40),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.info.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.info.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline, color: AppColors.info, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Compte de test',
                              style: TextStyle(
                                color: AppColors.info,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getTestAccountInfo(),
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool isPassword = false,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: isPassword && !_isPasswordVisible,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: AppColors.textSecondary),
            suffixIcon: isPassword
                ? IconButton(
                    icon: Icon(
                      _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  )
                : null,
            filled: true,
            fillColor: AppColors.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.textHint.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.textHint.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: _getThemeColor(), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  String _getSubtitle() {
    switch (widget.userType) {
      case UserType.user:
        return 'Connectez-vous pour réserver vos courses';
      case UserType.driver:
        return 'Connectez-vous pour commencer à conduire';
      case UserType.admin:
        return 'Accédez au panneau d\'administration';
    }
  }

  Color _getThemeColor() {
    switch (widget.userType) {
      case UserType.user:
        return AppColors.primary;
      case UserType.driver:
        return AppColors.secondary;
      case UserType.admin:
        return AppColors.accent;
    }
  }

  String _getTestAccountInfo() {
    switch (widget.userType) {
      case UserType.user:
        return 'Email: <EMAIL>\nMot de passe: password123';
      case UserType.driver:
        return 'Email: <EMAIL>\nMot de passe: password123';
      case UserType.admin:
        return 'Email: <EMAIL>\nMot de passe: admin123';
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return AppStrings.invalidEmail;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value.length < 6) {
      return AppStrings.passwordTooShort;
    }
    return null;
  }

  void _onLogin() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulation d'une connexion
      await Future.delayed(const Duration(seconds: 2));

      // Vérification des identifiants mock
      bool isValidLogin = false;
      Widget? nextScreen;

      switch (widget.userType) {
        case UserType.user:
          if (_emailController.text == MockDataService.mockUser.email &&
              _passwordController.text == 'password123') {
            isValidLogin = true;
            nextScreen = const UserHomeScreen();
          }
          break;
        case UserType.driver:
          if (_emailController.text == MockDataService.mockDriver.email &&
              _passwordController.text == 'password123') {
            isValidLogin = true;
            nextScreen = const DriverHomeScreen();
          }
          break;
        case UserType.admin:
          if (_emailController.text == MockDataService.mockAdmin.email &&
              _passwordController.text == 'admin123') {
            isValidLogin = true;
            nextScreen = const AdminDashboardScreen();
          }
          break;
      }

      setState(() {
        _isLoading = false;
      });

      if (isValidLogin && nextScreen != null) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => nextScreen!),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(AppStrings.loginFailed),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _onForgotPassword() {
    // TODO: Implémenter la récupération de mot de passe
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité à venir'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _onGoToRegister() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegisterScreen(userType: widget.userType),
      ),
    );
  }
}
