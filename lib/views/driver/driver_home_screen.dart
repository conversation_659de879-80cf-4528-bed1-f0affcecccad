import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:bili_taxi/constants/app_colors.dart';
import 'package:bili_taxi/constants/app_strings.dart';
import 'package:bili_taxi/constants/app_typography.dart';
import 'package:bili_taxi/widgets/modern_button.dart';
import 'package:bili_taxi/widgets/modern_card.dart';
import 'package:bili_taxi/models/ride.dart';
import 'package:bili_taxi/services/mock_data_service.dart';

import 'package:bili_taxi/viewmodels/driver_home_viewmodel.dart';

class DriverHomeScreen extends StatefulWidget {
  const DriverHomeScreen({Key? key}) : super(key: key);

  @override
  State<DriverHomeScreen> createState() => _DriverHomeScreenState();
}

class _DriverHomeScreenState extends State<DriverHomeScreen> {
  int _currentIndex = 0;
  late DriverHomeViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = DriverHomeViewModel();
    _viewModel.initialize();
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: IndexedStack(
          index: _currentIndex,
          children: [
            _buildMapScreen(),
            _buildEarningsScreen(),
            _buildProfileScreen(),
          ],
        ),
        bottomNavigationBar: _buildBottomNavigationBar(),
      ),
    );
  }

  Widget _buildMapScreen() {
    return Consumer<DriverHomeViewModel>(
      builder: (context, viewModel, child) {
        return SafeArea(
          child: Column(
            children: [
              // Header avec statut
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: viewModel.getStatusColor(),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 25,
                      backgroundImage: NetworkImage(
                        MockDataService.mockDriver.profileImage ?? '',
                      ),
                      backgroundColor: AppColors.secondary,
                      child: MockDataService.mockDriver.profileImage == null
                          ? Text(
                              MockDataService.mockDriver.firstName[0],
                              style: const TextStyle(
                                color: AppColors.textLight,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Bonjour, ${MockDataService.mockDriver.firstName}!',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppColors.textLight,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            viewModel.getStatusText(),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textLight.withAlpha(230),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Bouton de statut
                    GestureDetector(
                      onTap: () => viewModel.toggleDriverStatus(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.textLight.withAlpha(51),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: AppColors.textLight.withAlpha(128),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: viewModel.isOnline
                                    ? AppColors.success 
                                    : AppColors.textHint,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              viewModel.isOnline
                                  ? AppStrings.online 
                                  : AppStrings.offline,
                              style: const TextStyle(
                                color: AppColors.textLight,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Statistiques du jour
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatCard(
                      title: 'Courses',
                      value: viewModel.todayTrips.toString(),
                      icon: Icons.local_taxi,
                    ),
                    _buildStatCard(
                      title: 'Gains',
                      value: '€${viewModel.todayEarnings.toStringAsFixed(0)}',
                      icon: Icons.euro,
                    ),
                    _buildStatCard(
                      title: 'Temps',
                      value: viewModel.todayWorkingTime,
                      icon: Icons.access_time,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Zone de carte
          Expanded(
            child: Stack(
              children: [
                // Simulation de carte
                Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.mapBackground,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.mapBackground,
                              AppColors.mapBackground.withAlpha(204),
                            ],
                          ),
                        ),
                        child: viewModel.currentLocation != null
                            ? GoogleMap(
                                initialCameraPosition: CameraPosition(
                                  target: LatLng(
                                    viewModel.currentLocation!.latitude,
                                    viewModel.currentLocation!.longitude,
                                  ),
                                  zoom: 15.0,
                                ),
                                markers: viewModel.markers,
                                polylines: viewModel.polylines,
                                onMapCreated: viewModel.onMapCreated,
                                myLocationEnabled: true,
                                myLocationButtonEnabled: true,
                                zoomControlsEnabled: false,
                                mapToolbarEnabled: false,
                              )
                            : const Center(
                                child: CircularProgressIndicator(
                                  color: AppColors.primary,
                                ),
                              ),
                      ),
                      
                      // Position du conducteur
                      const Positioned(
                        left: 150,
                        top: 200,
                        child: Icon(
                          Icons.navigation,
                          color: AppColors.secondary,
                          size: 30,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Demande de course entrante
                if (viewModel.hasIncomingRide)
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: _buildIncomingRideCard(viewModel),
                  ),

                // Contrôles de course active
                if (viewModel.currentRide != null && !viewModel.hasIncomingRide)
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: _buildActiveRideControls(viewModel),
                  ),
              ],
            ),
          ),
          ],
        ),
      );
        },
      );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.textLight.withAlpha(38),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: AppColors.textLight,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: AppColors.textLight,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: AppColors.textLight.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomingRideCard(DriverHomeViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundImage: NetworkImage(
                  MockDataService.mockUser.profileImage ?? '',
                ),
                backgroundColor: AppColors.primary,
                radius: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      MockDataService.mockUser.fullName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: AppColors.secondary,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          MockDataService.mockUser.rating.toStringAsFixed(1),
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '€6.50',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Text(
                    '1.2 km • 8 min',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Adresses
          Row(
            children: [
              Column(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Container(
                    width: 2,
                    height: 30,
                    color: AppColors.textHint,
                  ),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                      color: AppColors.secondary,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '1 Rue de Rivoli, 75001 Paris',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Place Vendôme, 75001 Paris',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Boutons d'action
          Row(
            children: [
              Expanded(
                child: TaxiButton.rejectRide(
                  onPressed: () => viewModel.declineRide(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TaxiButton.acceptRide(
                  onPressed: () {
                    viewModel.acceptRide();
                    _showRideAccepted();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsScreen() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.earnings,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // Résumé des gains
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.secondaryGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.secondary.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppStrings.totalEarnings,
                            style: TextStyle(
                              color: AppColors.textLight.withOpacity(0.9),
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            '€${MockDataService.mockDriver.totalEarnings.toStringAsFixed(0)}',
                            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                              color: AppColors.textLight,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            AppStrings.todayEarnings,
                            style: TextStyle(
                              color: AppColors.textLight.withOpacity(0.9),
                              fontSize: 14,
                            ),
                          ),
                          const Text(
                            '€145',
                            style: TextStyle(
                              color: AppColors.textLight,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildEarningsStat('Courses', '${MockDataService.mockDriver.totalTrips}'),
                      _buildEarningsStat('Note', MockDataService.mockDriver.driverRating.toStringAsFixed(1)),
                      _buildEarningsStat('Heures', '6h 30m'),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Historique des gains
            Text(
              'Historique des gains',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: ListView.builder(
                itemCount: 7, // Derniers 7 jours
                itemBuilder: (context, index) {
                  final day = DateTime.now().subtract(Duration(days: index));
                  final earnings = (100 + (index * 20)) + (index % 3 * 15);
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _formatDate(day),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${8 + (index % 3)} courses',
                              style: const TextStyle(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          '€$earnings',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppColors.secondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: AppColors.textLight,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textLight.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildProfileScreen() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header profil conducteur
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: NetworkImage(
                      MockDataService.mockDriver.profileImage ?? '',
                    ),
                    backgroundColor: AppColors.secondary,
                    child: MockDataService.mockDriver.profileImage == null
                        ? Text(
                            MockDataService.mockDriver.firstName[0],
                            style: const TextStyle(
                              color: AppColors.textLight,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    MockDataService.mockDriver.fullName,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Conducteur professionnel',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: [
                          Text(
                            MockDataService.mockDriver.totalTrips.toString(),
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppColors.secondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Courses',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star,
                                color: AppColors.secondary,
                                size: 20,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                MockDataService.mockDriver.driverRating.toStringAsFixed(1),
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  color: AppColors.secondary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            'Note',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Text(
                            '€${MockDataService.mockDriver.totalEarnings.toStringAsFixed(0)}',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppColors.secondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Gains',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Options du profil conducteur
            Expanded(
              child: ListView(
                children: [
                  _buildProfileOption(
                    icon: Icons.person_outline,
                    title: AppStrings.editProfile,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.car_rental,
                    title: 'Mon véhicule',
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.description,
                    title: 'Documents',
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.schedule,
                    title: 'Horaires de travail',
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.payment,
                    title: 'Paiements',
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.notifications,
                    title: AppStrings.notifications,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.help_outline,
                    title: AppStrings.helpSupport,
                    onTap: () {},
                  ),
                  const SizedBox(height: 20),
                  _buildProfileOption(
                    icon: Icons.logout,
                    title: AppStrings.logout,
                    onTap: _onLogout,
                    isDestructive: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? AppColors.error : AppColors.textSecondary,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? AppColors.error : AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.textSecondary,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: AppColors.surface,
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.secondary,
        unselectedItemColor: AppColors.textSecondary,
        elevation: 0,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.map),
            label: AppStrings.map,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.euro),
            label: AppStrings.earnings,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: AppStrings.profile,
          ),
        ],
      ),
    );
  }



  void _showRideAccepted() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'Course acceptée !',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Dirigez-vous vers le point de prise en charge',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Commencer la navigation'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return "Aujourd'hui";
    } else if (difference == 1) {
      return 'Hier';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _onLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Déconnexion'),
        content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/',
                (route) => false,
              );
            },
            child: const Text('Déconnexion'),
          ),
        ],
      ),
    );
  }

  // Interface de contrôle de course active
  Widget _buildActiveRideControls(DriverHomeViewModel viewModel) {
    final ride = viewModel.currentRide!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // En-tête avec statut
          _buildRideStatusHeader(viewModel),

          const SizedBox(height: 16),

          // Informations de la course
          _buildActiveRideInfo(ride),

          const SizedBox(height: 16),

          // Boutons de contrôle selon l'état
          _buildControlButtons(viewModel),
        ],
      ),
    );
  }

  // En-tête avec statut de la course
  Widget _buildRideStatusHeader(DriverHomeViewModel viewModel) {
    String title;
    String subtitle;
    Color color;
    IconData icon;

    if (viewModel.isWaitingForUserAcceptance) {
      title = 'En attente de confirmation';
      subtitle = 'L\'utilisateur examine votre profil...';
      color = AppColors.secondary;
      icon = Icons.hourglass_empty;
    } else if (viewModel.isUserAccepted) {
      title = 'Course confirmée !';
      subtitle = 'Prêt à aller chercher le client';
      color = AppColors.success;
      icon = Icons.check_circle;
    } else if (viewModel.currentRide?.status == RideStatus.driverArriving) {
      title = 'En route vers le client';
      subtitle = 'Conduisez prudemment';
      color = AppColors.primary;
      icon = Icons.navigation;
    } else if (viewModel.currentRide?.status == RideStatus.driverArrived) {
      title = 'Arrivé au point de prise en charge';
      subtitle = 'Attendez que le client monte';
      color = AppColors.secondary;
      icon = Icons.location_on;
    } else if (viewModel.currentRide?.status == RideStatus.inProgress) {
      title = 'Course en cours';
      subtitle = 'Bon voyage !';
      color = AppColors.primary;
      icon = Icons.local_taxi;
    } else {
      title = 'Course active';
      subtitle = 'En cours...';
      color = AppColors.primary;
      icon = Icons.info;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(76)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: AppColors.textLight, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Informations de la course active
  Widget _buildActiveRideInfo(ride) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Trajet
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  ride.pickupLocation.address ?? 'Point de prise en charge',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
          Container(
            margin: const EdgeInsets.only(left: 4, top: 4, bottom: 4),
            width: 2,
            height: 16,
            color: AppColors.textHint,
          ),
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: AppColors.secondary,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  ride.destinationLocation.address ?? 'Destination',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Détails
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildActiveRideInfoItem('Distance', '${ride.estimatedDistance?.toStringAsFixed(1) ?? '0'} km'),
              _buildActiveRideInfoItem('Durée', '${ride.estimatedDuration ?? 0} min'),
              _buildActiveRideInfoItem('Prix', '${ride.estimatedFare?.toStringAsFixed(2) ?? '0'} €'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActiveRideInfoItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  // Boutons de contrôle selon l'état
  Widget _buildControlButtons(DriverHomeViewModel viewModel) {
    if (viewModel.isWaitingForUserAcceptance) {
      // En attente de l'acceptation utilisateur - pas de boutons
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.secondary.withAlpha(25),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                color: AppColors.secondary,
                strokeWidth: 2,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'En attente de la réponse du client...',
              style: TextStyle(
                color: AppColors.secondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } else if (viewModel.isUserAccepted) {
      // Utilisateur a accepté - bouton moderne "Aller au pickup"
      return ModernButton(
        text: 'Aller chercher le client',
        onPressed: () => viewModel.goToPickup(),
        style: ModernButtonStyle.primary,
        size: ModernButtonSize.large,
        icon: Icons.navigation,
        isFullWidth: true,
      );
    } else if (viewModel.currentRide?.status == RideStatus.driverArriving) {
      // En route - bouton moderne "Je suis arrivé"
      return ModernButton(
        text: 'Je suis arrivé',
        onPressed: () => viewModel.arriveAtPickup(),
        style: ModernButtonStyle.secondary,
        size: ModernButtonSize.large,
        icon: Icons.location_on,
        isFullWidth: true,
      );
    } else if (viewModel.currentRide?.status == RideStatus.driverArrived) {
      // Arrivé - bouton moderne "Démarrer la course"
      return TaxiButton.startTrip(
        onPressed: () => viewModel.startTrip(),
      );
    } else if (viewModel.currentRide?.status == RideStatus.inProgress) {
      // Course en cours - bouton moderne "Terminer la course"
      return TaxiButton.completeTrip(
        onPressed: () => viewModel.completeTrip(),
      );
    }

    // État par défaut
    return Container();
  }
}
