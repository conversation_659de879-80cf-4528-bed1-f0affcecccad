import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:bili_taxi/constants/app_colors.dart';
import 'package:bili_taxi/constants/app_strings.dart';
import 'package:bili_taxi/constants/app_typography.dart';
import 'package:bili_taxi/widgets/modern_button.dart';
import 'package:bili_taxi/widgets/modern_card.dart';
import 'package:bili_taxi/services/mock_data_service.dart';
import 'package:bili_taxi/models/vehicle.dart';
import 'package:bili_taxi/models/location.dart';
import 'package:bili_taxi/models/payment.dart';
import 'package:bili_taxi/viewmodels/user_home_viewmodel.dart';

class UserHomeScreen extends StatefulWidget {
  const UserHomeScreen({Key? key}) : super(key: key);

  @override
  State<UserHomeScreen> createState() => _UserHomeScreenState();
}

class _UserHomeScreenState extends State<UserHomeScreen> {
  int _currentIndex = 0;
  late UserHomeViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = UserHomeViewModel();
    _viewModel.initialize();
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: IndexedStack(
          index: _currentIndex,
          children: [
            _buildMapScreen(),
            _buildRidesScreen(),
            _buildProfileScreen(),
          ],
        ),
        bottomNavigationBar: _buildBottomNavigationBar(),
      ),
    );
  }

  Widget _buildMapScreen() {
    return Consumer<UserHomeViewModel>(
      builder: (context, viewModel, child) {
        return SafeArea(
          child: Column(
            children: [
              // Header avec salutation
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: AppColors.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 25,
                          backgroundImage: NetworkImage(
                            MockDataService.mockUser.profileImage ?? '',
                          ),
                          backgroundColor: AppColors.primary,
                          child: MockDataService.mockUser.profileImage == null
                              ? Text(
                                  MockDataService.mockUser.firstName[0],
                                  style: const TextStyle(
                                    color: AppColors.textLight,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : null,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Bonjour, ${MockDataService.mockUser.firstName}!',
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  color: AppColors.textPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                AppStrings.whereToGo,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(25),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star,
                                color: AppColors.primary,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                MockDataService.mockUser.rating.toStringAsFixed(1),
                                style: const TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Zone de carte Google Maps
              Expanded(
                flex: 3,
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: viewModel.currentLocation != null
                        ? GoogleMap(
                            initialCameraPosition: CameraPosition(
                              target: LatLng(
                                viewModel.currentLocation!.latitude,
                                viewModel.currentLocation!.longitude,
                              ),
                              zoom: 14.0,
                            ),
                            markers: viewModel.markers,
                            polylines: viewModel.polylines,
                            onMapCreated: viewModel.onMapCreated,
                            myLocationEnabled: true,
                            myLocationButtonEnabled: false,
                            zoomControlsEnabled: false,
                            mapToolbarEnabled: false,
                            onTap: (LatLng position) {
                              // Permettre la sélection de destination sur la carte
                              _showLocationSelectionDialog(context, position, viewModel);
                            },
                          )
                        : Container(
                            color: AppColors.mapBackground,
                            child: const Center(
                              child: CircularProgressIndicator(
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                  ),
                ),
              ),
          
          // Section de réservation ou course active
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 8,
                  offset: Offset(0, -4),
                ),
              ],
            ),
            child: viewModel.isRideActive && viewModel.currentRide != null
                ? _buildActiveRideInterface(viewModel)
                : _buildBookingInterface(viewModel),
          ),
          ],
        ),
      );
        },
      );
  }

  // Interface de réservation (état initial)
  Widget _buildBookingInterface(UserHomeViewModel viewModel) {
    return Column(
      children: [
        // Barre de recherche de destination
        GestureDetector(
          onTap: () => _showDestinationSearch(context, viewModel),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.textHint.withAlpha(76),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    viewModel.destinationLocation?.address ?? AppStrings.searchLocation,
                    style: TextStyle(
                      color: viewModel.destinationLocation != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        if (viewModel.destinationLocation != null) ...[
          const SizedBox(height: 16),

          // Sélection du type de véhicule
          _buildVehicleTypeSelector(viewModel),

          const SizedBox(height: 16),

          // Sélection de la méthode de paiement
          _buildPaymentMethodSelector(viewModel),

          const SizedBox(height: 16),

          // Informations de course
          _buildRideInfo(viewModel),

          const SizedBox(height: 20),

          // Bouton de réservation
          Container(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: viewModel.isRequestingRide ? null : () => viewModel.requestRide(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textLight,
                elevation: 8,
                shadowColor: AppColors.primary.withAlpha(76),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: viewModel.isRequestingRide
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: AppColors.textLight,
                            strokeWidth: 2,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          AppStrings.searchingDriver,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.textLight,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      AppStrings.requestRide,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textLight,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ],
    );
  }

  // Interface de course active
  Widget _buildActiveRideInterface(UserHomeViewModel viewModel) {
    final ride = viewModel.currentRide!;

    // Interface différente selon l'état de la course
    if (viewModel.isSearchingForDriver) {
      return _buildSearchingDriverInterface(viewModel);
    } else if (viewModel.isDriverAccepted) {
      return _buildDriverAcceptedInterface(viewModel);
    } else if (viewModel.isUserAccepted) {
      return _buildUserAcceptedInterface(viewModel);
    } else if (viewModel.isDriverArriving) {
      return _buildDriverArrivingInterface(viewModel);
    } else if (viewModel.isDriverArrived) {
      return _buildDriverArrivedInterface(viewModel);
    } else if (viewModel.isRideInProgress) {
      return _buildRideInProgressInterface(viewModel);
    } else if (viewModel.isRideCompleted) {
      return _buildRideCompletedInterface(viewModel);
    } else if (viewModel.isRideFailed) {
      return _buildRideFailedInterface(viewModel);
    } else if (viewModel.isUserRejected) {
      return _buildUserRejectedInterface(viewModel);
    }

    // Interface par défaut
    return _buildDefaultRideInterface(viewModel);
  }

  // Interface de recherche de conducteur - Design moderne
  Widget _buildSearchingDriverInterface(UserHomeViewModel viewModel) {
    return Column(
      children: [
        // Carte de statut moderne
        TaxiCard.rideStatus(
          title: 'Recherche en cours...',
          subtitle: 'Nous cherchons un conducteur disponible',
          icon: Icons.search,
          color: AppColors.primary,
          action: const SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              color: AppColors.primary,
              strokeWidth: 2,
            ),
          ),
        ),

        const SizedBox(height: 16),

        _buildModernRideDetails(viewModel),

        const SizedBox(height: 20),

        _buildModernCancelButton(viewModel),
      ],
    );
  }

  // Interface conducteur accepté (attend confirmation utilisateur) - Design moderne
  Widget _buildDriverAcceptedInterface(UserHomeViewModel viewModel) {
    return Column(
      children: [
        // Carte de statut moderne
        TaxiCard.rideStatus(
          title: 'Conducteur trouvé !',
          subtitle: 'Acceptez-vous ce conducteur ?',
          icon: Icons.person_search,
          color: AppColors.secondary,
        ),

        const SizedBox(height: 16),

        // Informations du conducteur avec TaxiCard
        if (viewModel.assignedDriver != null)
          TaxiCard.driverInfo(
            driverName: '${viewModel.assignedDriver!.firstName} ${viewModel.assignedDriver!.lastName}',
            vehicleInfo: viewModel.assignedDriver!.vehicle != null
                ? '${viewModel.assignedDriver!.vehicle!.make} ${viewModel.assignedDriver!.vehicle!.model}'
                : 'Véhicule non spécifié',
            rating: viewModel.assignedDriver!.driverRating,
            profileImage: viewModel.assignedDriver!.profileImage,
          ),

        const SizedBox(height: 16),

        _buildModernRideDetails(viewModel),

        const SizedBox(height: 20),

        // Boutons modernes d'acceptation/refus
        Row(
          children: [
            Expanded(
              child: TaxiButton.rejectRide(
                onPressed: () => viewModel.rejectDriver(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TaxiButton.acceptRide(
                onPressed: () => viewModel.acceptDriver(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Interface utilisateur a accepté le conducteur - Design moderne
  Widget _buildUserAcceptedInterface(UserHomeViewModel viewModel) {
    return Column(
      children: [
        // Carte de statut moderne
        TaxiCard.rideStatus(
          title: 'Conducteur confirmé !',
          subtitle: 'Votre conducteur se prépare à venir',
          icon: Icons.check_circle,
          color: AppColors.success,
        ),

        const SizedBox(height: 16),

        // Informations du conducteur avec TaxiCard
        if (viewModel.assignedDriver != null)
          TaxiCard.driverInfo(
            driverName: '${viewModel.assignedDriver!.firstName} ${viewModel.assignedDriver!.lastName}',
            vehicleInfo: viewModel.assignedDriver!.vehicle != null
                ? '${viewModel.assignedDriver!.vehicle!.make} ${viewModel.assignedDriver!.vehicle!.model}'
                : 'Véhicule non spécifié',
            rating: viewModel.assignedDriver!.driverRating,
            profileImage: viewModel.assignedDriver!.profileImage,
          ),

        const SizedBox(height: 16),

        _buildModernRideDetails(viewModel),

        const SizedBox(height: 20),

        _buildModernCancelButton(viewModel),
      ],
    );
  }

  // Interface utilisateur a refusé le conducteur - Design moderne
  Widget _buildUserRejectedInterface(UserHomeViewModel viewModel) {
    return Column(
      children: [
        // Carte de statut moderne
        TaxiCard.rideStatus(
          title: 'Recherche d\'un autre conducteur...',
          subtitle: 'Nous cherchons une alternative',
          icon: Icons.refresh,
          color: AppColors.warning,
          action: const SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              color: AppColors.warning,
              strokeWidth: 2,
            ),
          ),
        ),

        const SizedBox(height: 16),

        _buildModernRideDetails(viewModel),

        const SizedBox(height: 20),

        _buildModernCancelButton(viewModel),
      ],
    );
  }

  // Méthodes utilitaires pour les interfaces de course
  Widget _buildDriverArrivingInterface(UserHomeViewModel viewModel) {
    return _buildDefaultRideInterface(viewModel,
      title: 'Conducteur en route',
      subtitle: 'Arrivée estimée dans 5 minutes',
      color: AppColors.secondary);
  }

  Widget _buildDriverArrivedInterface(UserHomeViewModel viewModel) {
    return _buildDefaultRideInterface(viewModel,
      title: 'Conducteur arrivé',
      subtitle: 'Votre conducteur vous attend',
      color: AppColors.success);
  }

  Widget _buildRideInProgressInterface(UserHomeViewModel viewModel) {
    return _buildDefaultRideInterface(viewModel,
      title: 'Course en cours',
      subtitle: 'Bon voyage !',
      color: AppColors.primary);
  }

  Widget _buildRideCompletedInterface(UserHomeViewModel viewModel) {
    return _buildDefaultRideInterface(viewModel,
      title: 'Course terminée',
      subtitle: 'Merci d\'avoir utilisé notre service',
      color: AppColors.success);
  }

  Widget _buildRideFailedInterface(UserHomeViewModel viewModel) {
    return _buildDefaultRideInterface(viewModel,
      title: 'Course échouée',
      subtitle: 'Aucun conducteur disponible',
      color: AppColors.error);
  }

  Widget _buildDefaultRideInterface(UserHomeViewModel viewModel, {
    String title = 'Course active',
    String subtitle = 'En cours...',
    Color color = AppColors.primary,
  }) {
    return Column(
      children: [
        // En-tête
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withAlpha(25),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withAlpha(76)),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.local_taxi, color: AppColors.textLight, size: 16),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: color, fontWeight: FontWeight.bold)),
                    Text(subtitle, style: TextStyle(color: AppColors.textSecondary, fontSize: 14)),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        _buildRideDetails(viewModel),
        const SizedBox(height: 20),
        _buildCancelButton(viewModel),
      ],
    );
  }

  Widget _buildDriverInfo(driver) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundImage: driver.profileImage != null
              ? NetworkImage(driver.profileImage!)
              : null,
            backgroundColor: AppColors.primary,
            child: driver.profileImage == null
              ? const Icon(Icons.person, color: AppColors.textLight)
              : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${driver.firstName} ${driver.lastName}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold)),
                Text(driver.vehicle != null
                  ? '${driver.vehicle!.make} ${driver.vehicle!.model}'
                  : 'Véhicule non spécifié',
                  style: TextStyle(color: AppColors.textSecondary)),
                Text('⭐ ${driver.driverRating.toStringAsFixed(1)}',
                  style: TextStyle(color: AppColors.secondary)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRideDetails(UserHomeViewModel viewModel) {
    final ride = viewModel.currentRide!;
    return Column(
      children: [
        // Trajet
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(width: 12, height: 12, decoration: const BoxDecoration(
                    color: AppColors.primary, shape: BoxShape.circle)),
                  const SizedBox(width: 12),
                  Expanded(child: Text(ride.pickupLocation.address ?? 'Position actuelle')),
                ],
              ),
              Container(margin: const EdgeInsets.only(left: 6, top: 8, bottom: 8),
                width: 2, height: 20, color: AppColors.textHint),
              Row(
                children: [
                  Container(width: 12, height: 12, decoration: BoxDecoration(
                    color: AppColors.secondary, shape: BoxShape.circle)),
                  const SizedBox(width: 12),
                  Expanded(child: Text(ride.destinationLocation.address ?? 'Destination')),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Détails
        Row(
          children: [
            Expanded(child: _buildDetailCard('Temps estimé', '${ride.estimatedDuration ?? 8} min')),
            const SizedBox(width: 12),
            Expanded(child: _buildDetailCard('Prix', '${ride.estimatedFare?.toStringAsFixed(2) ?? '6.50'} €')),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailCard(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(label, style: TextStyle(color: AppColors.textSecondary, fontSize: 12)),
          const SizedBox(height: 4),
          Text(value, style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: label == 'Prix' ? AppColors.primary : AppColors.textPrimary,
            fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildCancelButton(UserHomeViewModel viewModel) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () => _showCancelRideDialog(context, viewModel),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.textLight,
          elevation: 8,
          shadowColor: AppColors.error.withAlpha(76),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
        child: Text('Annuler la course',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.textLight, fontWeight: FontWeight.w600)),
      ),
    );
  }

  Widget _buildVehicleTypeSelector(UserHomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.selectVehicleType,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: VehicleType.values.map((type) {
              final isSelected = viewModel.selectedVehicleType == type;
              return GestureDetector(
                onTap: () {
                  viewModel.selectVehicleType(type);
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 12),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary : AppColors.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? AppColors.primary : AppColors.textHint.withAlpha(76),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        _getVehicleIcon(type),
                        color: isSelected ? AppColors.textLight : AppColors.textSecondary,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getVehicleTypeName(type),
                        style: TextStyle(
                          color: isSelected ? AppColors.textLight : AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodSelector(UserHomeViewModel viewModel) {
    // Only show cash and card options as requested
    final availablePaymentMethods = [PaymentMethod.card, PaymentMethod.cash];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.selectPaymentMethod,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: availablePaymentMethods.map((method) {
            final isSelected = viewModel.selectedPaymentMethod == method;
            return Expanded(
              child: GestureDetector(
                onTap: () {
                  viewModel.selectPaymentMethod(method);
                },
                child: Container(
                  margin: EdgeInsets.only(
                    right: method == availablePaymentMethods.first ? 12 : 0,
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary : AppColors.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? AppColors.primary : AppColors.textHint.withAlpha(76),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        _getPaymentMethodIcon(method),
                        color: isSelected ? AppColors.textLight : AppColors.textSecondary,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getPaymentMethodName(method),
                        style: TextStyle(
                          color: isSelected ? AppColors.textLight : AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRideInfo(UserHomeViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.estimatedTime,
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    viewModel.rideEstimation?['durationText'] ?? '8 min',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.distance,
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    viewModel.rideEstimation?['distanceText'] ?? '1.2 km',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.estimatedPrice,
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    viewModel.rideEstimation?['priceText'] ?? '6.50 €',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRidesScreen() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.history,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: MockDataService.mockRides.length,
                itemBuilder: (context, index) {
                  final ride = MockDataService.mockRides[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(13),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              ride.pickupLocation.address ?? 'Adresse inconnue',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.success.withAlpha(25),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                AppStrings.rideCompleted,
                                style: TextStyle(
                                  color: AppColors.success,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          ride.destinationLocation.address ?? 'Destination inconnue',
                          style: TextStyle(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${ride.actualFare?.toStringAsFixed(2)} €',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                ...List.generate(5, (starIndex) {
                                  return Icon(
                                    starIndex < (ride.userRating ?? 0)
                                        ? Icons.star
                                        : Icons.star_border,
                                    color: AppColors.secondary,
                                    size: 16,
                                  );
                                }),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileScreen() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header profil
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: NetworkImage(
                      MockDataService.mockUser.profileImage ?? '',
                    ),
                    backgroundColor: AppColors.primary,
                    child: MockDataService.mockUser.profileImage == null
                        ? Text(
                            MockDataService.mockUser.firstName[0],
                            style: const TextStyle(
                              color: AppColors.textLight,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    MockDataService.mockUser.fullName,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    MockDataService.mockUser.email,
                    style: TextStyle(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: [
                          Text(
                            MockDataService.mockUser.totalRides.toString(),
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Courses',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star,
                                color: AppColors.secondary,
                                size: 20,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                MockDataService.mockUser.rating.toStringAsFixed(1),
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  color: AppColors.secondary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            'Note',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Options du profil
            Expanded(
              child: ListView(
                children: [
                  _buildProfileOption(
                    icon: Icons.person_outline,
                    title: AppStrings.editProfile,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.payment,
                    title: AppStrings.paymentMethod,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.favorite_outline,
                    title: AppStrings.favorites,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.notifications,
                    title: AppStrings.notifications,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.help_outline,
                    title: AppStrings.helpSupport,
                    onTap: () {},
                  ),
                  _buildProfileOption(
                    icon: Icons.info_outline,
                    title: AppStrings.aboutApp,
                    onTap: () {},
                  ),
                  const SizedBox(height: 20),
                  _buildProfileOption(
                    icon: Icons.logout,
                    title: AppStrings.logout,
                    onTap: _onLogout,
                    isDestructive: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? AppColors.error : AppColors.textSecondary,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? AppColors.error : AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.textSecondary,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: AppColors.surface,
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        elevation: 0,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.map),
            label: AppStrings.map,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: AppStrings.rides,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: AppStrings.profile,
          ),
        ],
      ),
    );
  }

  IconData _getVehicleIcon(VehicleType type) {
    switch (type) {
      case VehicleType.economy:
        return Icons.directions_car;
      case VehicleType.comfort:
        return Icons.car_rental;
      case VehicleType.premium:
        return Icons.star;
      case VehicleType.luxury:
        return Icons.diamond;
      case VehicleType.suv:
        return Icons.airport_shuttle;
    }
  }

  String _getVehicleTypeName(VehicleType type) {
    switch (type) {
      case VehicleType.economy:
        return AppStrings.economy;
      case VehicleType.comfort:
        return AppStrings.comfort;
      case VehicleType.premium:
        return AppStrings.premium;
      case VehicleType.luxury:
        return AppStrings.luxury;
      case VehicleType.suv:
        return AppStrings.suv;
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.stripe:
        return Icons.payment;
      case PaymentMethod.paypal:
        return Icons.account_balance_wallet;
      case PaymentMethod.applePay:
        return Icons.apple;
      case PaymentMethod.googlePay:
        return Icons.account_balance_wallet;
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return AppStrings.cash;
      case PaymentMethod.card:
        return 'Avec l\'application';
      case PaymentMethod.stripe:
        return AppStrings.stripe;
      case PaymentMethod.paypal:
        return AppStrings.paypal;
      case PaymentMethod.applePay:
        return AppStrings.applePay;
      case PaymentMethod.googlePay:
        return AppStrings.googlePay;
    }
  }

  void _showDestinationSearch(BuildContext context, UserHomeViewModel viewModel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _DestinationSearchBottomSheet(viewModel: viewModel),
    );
  }

  void _showCancelRideDialog(BuildContext context, UserHomeViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Annuler la course'),
        content: const Text('Êtes-vous sûr de vouloir annuler cette course ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Non'),
          ),
          TextButton(
            onPressed: () {
              viewModel.cancelRide();
              Navigator.pop(context);
            },
            child: const Text(
              'Oui, annuler',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showLocationSelectionDialog(BuildContext context, LatLng position, UserHomeViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sélectionner cette position'),
        content: const Text('Voulez-vous utiliser cette position comme destination ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              final location = Location(
                latitude: position.latitude,
                longitude: position.longitude,
                address: 'Position sélectionnée',
                timestamp: DateTime.now(),
              );
              viewModel.setDestination(location);
              Navigator.pop(context);
            },
            child: const Text('Confirmer'),
          ),
        ],
      ),
    );
  }

  void _onLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Déconnexion'),
        content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/',
                (route) => false,
              );
            },
            child: const Text('Déconnexion'),
          ),
        ],
      ),
    );
  }

  // === MÉTHODES MODERNES ===

  // Détails de course modernes avec TaxiCard
  Widget _buildModernRideDetails(UserHomeViewModel viewModel) {
    final ride = viewModel.currentRide!;

    return TaxiCard.tripDetails(
      pickup: ride.pickupLocation.address ?? 'Position actuelle',
      destination: ride.destinationLocation.address ?? 'Destination',
      distance: '${ride.estimatedDistance?.toStringAsFixed(1) ?? '1.2'} km',
      duration: '${ride.estimatedDuration ?? 8} min',
      price: '${ride.estimatedFare?.toStringAsFixed(2) ?? '6.50'} €',
    );
  }

  // Bouton d'annulation moderne
  Widget _buildModernCancelButton(UserHomeViewModel viewModel) {
    return TaxiButton.cancelRide(
      onPressed: () => _showCancelRideDialog(context, viewModel),
    );
  }
}

class _DestinationSearchBottomSheet extends StatefulWidget {
  final UserHomeViewModel viewModel;

  const _DestinationSearchBottomSheet({required this.viewModel});

  @override
  State<_DestinationSearchBottomSheet> createState() => _DestinationSearchBottomSheetState();
}

class _DestinationSearchBottomSheetState extends State<_DestinationSearchBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.viewModel,
      builder: (context, child) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: AppColors.textHint,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Choisir une destination',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    onChanged: (query) => widget.viewModel.searchPlaces(query),
                    decoration: InputDecoration(
                      hintText: AppStrings.searchLocation,
                      prefixIcon: const Icon(Icons.search),
                      filled: true,
                      fillColor: AppColors.background,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Affichage des résultats de recherche ou lieux favoris
                  if (widget.viewModel.searchResults.isNotEmpty) ...[
                    Text(
                      'Résultats de recherche',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    if (widget.viewModel.isSearching)
                      const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      )
                    else
                      ...widget.viewModel.searchResults.map((prediction) {
                        return ListTile(
                          leading: const Icon(Icons.location_on, color: AppColors.primary),
                          title: Text(prediction.mainText),
                          subtitle: Text(prediction.secondaryText),
                          onTap: () async {
                            await widget.viewModel.selectPlace(prediction);
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          },
                        );
                      }),
                  ] else ...[
                    Text(
                      'Lieux favoris',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...MockDataService.mockFavoriteLocations.map((location) {
                      return ListTile(
                        leading: const Icon(Icons.location_on, color: AppColors.primary),
                        title: Text(location.address ?? 'Adresse inconnue'),
                        subtitle: Text('${location.city}, ${location.country}'),
                        onTap: () {
                          widget.viewModel.setDestination(location);
                          Navigator.pop(context);
                        },
                      );
                    }),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
