// Tests pour l'application Bili Taxi

import 'package:flutter_test/flutter_test.dart';
import 'package:bili_taxi/main.dart';
import 'package:bili_taxi/constants/app_strings.dart';

void main() {
  testWidgets('App should start with account type selection', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const BiliTaxiApp());

    // Verify that the app starts with the account type selection screen
    expect(find.text(AppStrings.appName), findsOneWidget);
    expect(find.text(AppStrings.selectAccountType), findsOneWidget);
    expect(find.text(AppStrings.userAccount), findsOneWidget);
    expect(find.text(AppStrings.driverAccount), findsOneWidget);
    expect(find.text(AppStrings.adminAccount), findsOneWidget);
  });

  testWidgets('Should be able to select account type', (WidgetTester tester) async {
    await tester.pumpWidget(const BiliTaxiApp());

    // Tap on user account type
    await tester.tap(find.text(AppStrings.userAccount));
    await tester.pump();

    // Verify continue button is enabled
    expect(find.text('Continuer'), findsOneWidget);
  });
}
